from django.db import models

# Create your models here.
class RunData(models.Model):  # 记录用户信息
    mod_date = models.DateTimeField(verbose_name = '最后修改日期', auto_now=False)
    data = models.CharField(max_length=30, verbose_name="数据",default="")
    faultStatus = models.CharField(max_length=5,verbose_name="故障状态",default="0")
    healthStatus = models.CharField(max_length=5,verbose_name="健康状态",default="1")
    type = models.CharField(max_length=10,verbose_name="系统类别",default="0")


    def __str__(self):
        return str(self.mod_date) + self.data

    class Meta:
        verbose_name = verbose_name_plural = "数据"


