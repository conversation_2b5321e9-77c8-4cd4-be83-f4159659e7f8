import os
import logging
import datetime
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler

class PHMLogger:
    """
    PHM系统日志记录工具类
    提供不同类型日志的记录功能，包括系统运行日志、故障诊断日志和主控台操作日志
    """
    
    # 日志类型常量
    SYSTEM = 'system'       # 系统运行日志
    DIAGNOSIS = 'diagnosis' # 故障诊断日志
    CONSOLE = 'console'     # 主控台操作日志
    
    def __init__(self, log_dir=None):
        """
        初始化日志记录器
        :param log_dir: 日志保存目录，默认为项目根目录下的logs文件夹
        """
        # 确定日志保存目录
        if log_dir is None:
            # 获取当前文件所在目录的上两级目录（项目根目录）
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            self.log_dir = os.path.join(base_dir, 'logs')
        else:
            self.log_dir = log_dir
            
        # 确保日志目录存在
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
            
        # 创建各类型日志子目录
        self.system_log_dir = os.path.join(self.log_dir, 'system')
        self.diagnosis_log_dir = os.path.join(self.log_dir, 'diagnosis')
        self.console_log_dir = os.path.join(self.log_dir, 'console')
        
        for directory in [self.system_log_dir, self.diagnosis_log_dir, self.console_log_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
        
        # 初始化各类型日志记录器
        self.loggers = {}
        self._setup_logger(PHMLogger.SYSTEM)
        self._setup_logger(PHMLogger.DIAGNOSIS)
        self._setup_logger(PHMLogger.CONSOLE)
    
    def _setup_logger(self, logger_type):
        """
        设置指定类型的日志记录器
        :param logger_type: 日志类型
        """
        logger = logging.getLogger(f'phm.{logger_type}')
        logger.setLevel(logging.INFO)
        
        # 避免重复添加处理器
        if logger.handlers:
            return
        
        # 确定日志文件路径
        if logger_type == PHMLogger.SYSTEM:
            log_dir = self.system_log_dir
            # 系统日志按日期切分
            handler = TimedRotatingFileHandler(
                os.path.join(log_dir, f'{logger_type}.log'),
                when='midnight',
                interval=1,
                backupCount=30
            )
        elif logger_type == PHMLogger.DIAGNOSIS:
            log_dir = self.diagnosis_log_dir
            # 诊断日志按大小切分，每个文件最大10MB，最多保留50个文件
            handler = RotatingFileHandler(
                os.path.join(log_dir, f'{logger_type}.log'),
                maxBytes=10*1024*1024,
                backupCount=50
            )
        elif logger_type == PHMLogger.CONSOLE:
            log_dir = self.console_log_dir
            # 主控台日志按日期切分
            handler = TimedRotatingFileHandler(
                os.path.join(log_dir, f'{logger_type}.log'),
                when='midnight',
                interval=1,
                backupCount=30
            )
        else:
            raise ValueError(f"未知的日志类型: {logger_type}")
        
        # 设置日志格式
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        
        # 添加处理器到日志记录器
        logger.addHandler(handler)
        
        # 保存日志记录器引用
        self.loggers[logger_type] = logger
    
    def log(self, log_type, level, message, **kwargs):
        """
        记录日志
        :param log_type: 日志类型
        :param level: 日志级别 (debug, info, warning, error, critical)
        :param message: 日志消息
        :param kwargs: 额外的日志信息，将以JSON格式添加到日志中
        """
        if log_type not in self.loggers:
            raise ValueError(f"未知的日志类型: {log_type}")
        
        logger = self.loggers[log_type]
        
        # 添加额外信息
        if kwargs:
            import json
            extra_info = json.dumps(kwargs, ensure_ascii=False)
            full_message = f"{message} | 额外信息: {extra_info}"
        else:
            full_message = message
        
        # 根据级别记录日志
        if level.lower() == 'debug':
            logger.debug(full_message)
        elif level.lower() == 'info':
            logger.info(full_message)
        elif level.lower() == 'warning':
            logger.warning(full_message)
        elif level.lower() == 'error':
            logger.error(full_message)
        elif level.lower() == 'critical':
            logger.critical(full_message)
        else:
            logger.info(full_message)
    
    def system_log(self, level, message, **kwargs):
        """系统日志记录"""
        self.log(PHMLogger.SYSTEM, level, message, **kwargs)
    
    def diagnosis_log(self, level, message, **kwargs):
        """故障诊断日志记录"""
        self.log(PHMLogger.DIAGNOSIS, level, message, **kwargs)
    
    def console_log(self, level, message, **kwargs):
        """主控台操作日志记录"""
        self.log(PHMLogger.CONSOLE, level, message, **kwargs)
    
    def get_logger(self, log_type):
        """
        获取指定类型的日志记录器
        :param log_type: 日志类型
        :return: 日志记录器实例
        """
        if log_type not in self.loggers:
            raise ValueError(f"未知的日志类型: {log_type}")
        return self.loggers[log_type]

# 创建全局日志实例，便于导入使用
phm_logger = PHMLogger() 