{"displacement": [{"time": ["0.0", "0.01", "0.02", "0.03", "0.04", "0.05", "0.06", "0.07", "0.08", "0.09", "0.1", "0.11", "0.12", "0.13", "0.14", "0.15", "0.16", "0.17", "0.18", "0.19", "0.2", "0.21", "0.22", "0.23", "0.24", "0.25", "0.26", "0.27", "0.28", "0.29", "0.3", "0.31", "0.32", "0.33", "0.34", "0.35", "0.36", "0.37", "0.38", "0.39", "0.4", "0.41", "0.42", "0.43", "0.44", "0.45", "0.46", "0.47", "0.48", "0.49", "0.5", "0.51", "0.52", "0.53", "0.54", "0.55", "0.56", "0.57", "0.58", "0.59", "0.6", "0.61", "0.62", "0.63", "0.64", "0.65", "0.66", "0.67", "0.68", "0.69", "0.7", "0.71", "0.72", "0.73", "0.74", "0.75", "0.76", "0.77", "0.78", "0.79", "0.8", "0.81", "0.82", "0.83", "0.84", "0.85", "0.86", "0.87", "0.88", "0.89", "0.9", "0.91", "0.92", "0.93", "0.94", "0.95", "0.96", "0.97", "0.98", "0.99", "1.0", "1.01", "1.02", "1.03", "1.04", "1.05", "1.06", "1.07", "1.08", "1.09", "1.1", "1.11", "1.12", "1.13", "1.14", "1.15", "1.16", "1.17", "1.18", "1.19", "1.2", "1.21", "1.22", "1.23", "1.24", "1.25", "1.26", "1.27", "1.28", "1.29", "1.3", "1.31", "1.32", "1.33", "1.34", "1.35", "1.36", "1.37", "1.38", "1.39", "1.4", "1.41", "1.42", "1.43", "1.44", "1.45", "1.46", "1.47", "1.48", "1.49", "1.5", "1.51", "1.52", "1.53", "1.54", "1.55", "1.56", "1.57", "1.58", "1.59", "1.6", "1.61", "1.62", "1.63", "1.64", "1.65", "1.66", "1.67", "1.68", "1.69", "1.7", "1.71", "1.72", "1.73", "1.74", "1.75", "1.76", "1.77", "1.78", "1.79", "1.8", "1.81", "1.82", "1.83", "1.84", "1.85", "1.86", "1.87", "1.88", "1.89", "1.9", "1.91", "1.92", "1.93", "1.94", "1.95", "1.96", "1.97", "1.98", "1.99", "2.0", "2.01", "2.02", "2.03", "2.04", "2.05", "2.06", "2.07", "2.08", "2.09", "2.1", "2.11", "2.12", "2.13", "2.14", "2.15", "2.16", "2.17", "2.18", "2.19", "2.2", "2.21", "2.22", "2.23", "2.24", "2.25", "2.26", "2.27", "2.28", "2.29", "2.3", "2.31", "2.32", "2.33", "2.34", "2.35", "2.36", "2.37", "2.38", "2.39", "2.4", "2.41", "2.42", "2.43", "2.44", "2.45", "2.46", "2.47", "2.48", "2.49", "2.5", "2.51", "2.52", "2.53", "2.54", "2.55", "2.56", "2.57", "2.58", "2.59", "2.6", "2.61", "2.62", "2.63", "2.64", "2.65", "2.66", "2.67", "2.68", "2.69", "2.7", "2.71", "2.72", "2.73", "2.74", "2.75", "2.76", "2.77", "2.78", "2.79", "2.8", "2.81", "2.82", "2.83", "2.84", "2.85", "2.86", "2.87", "2.88", "2.89", "2.9", "2.91", "2.92", "2.93", "2.94", "2.95", "2.96", "2.97", "2.98", "2.99", "3.0", "3.01", "3.02", "3.03", "3.04", "3.05", "3.06", "3.07", "3.08", "3.09", "3.1", "3.11", "3.12", "3.13", "3.14", "3.15", "3.16", "3.17", "3.18", "3.19", "3.2", "3.21", "3.22", "3.23", "3.24", "3.25", "3.26", "3.27", "3.28", "3.29", "3.3", "3.31", "3.32", "3.33", "3.34", "3.35", "3.36", "3.37", "3.38", "3.39", "3.4", "3.41", "3.42", "3.43", "3.44", "3.45", "3.46", "3.47", "3.48", "3.49", "3.5", "3.51", "3.52", "3.53", "3.54", "3.55", "3.56", "3.57", "3.58", "3.59", "3.6", "3.61", "3.62", "3.63", "3.64", "3.65", "3.66", "3.67", "3.68", "3.69", "3.7", "3.71", "3.72", "3.73", "3.74", "3.75", "3.76", "3.77", "3.78", "3.79", "3.8", "3.81", "3.82", "3.83", "3.84", "3.85", "3.86", "3.87", "3.88", "3.89", "3.9", "3.91", "3.92", "3.93", "3.94", "3.95", "3.96", "3.97", "3.98", "3.99", "4.0", "4.01", "4.02", "4.03", "4.04", "4.05", "4.06", "4.07", "4.08", "4.09", "4.1", "4.11", "4.12", "4.13", "4.14", "4.15", "4.16", "4.17", "4.18", "4.19", "4.2", "4.21", "4.22", "4.23", "4.24", "4.25", "4.26", "4.27", "4.28", "4.29", "4.3", "4.31", "4.32", "4.33", "4.34", "4.35", "4.36", "4.37", "4.38", "4.39", "4.4", "4.41", "4.42", "4.43", "4.44", "4.45", "4.46", "4.47", "4.48", "4.49", "4.5", "4.51", "4.52", "4.53", "4.54", "4.55", "4.56", "4.57", "4.58", "4.59", "4.6", "4.61", "4.62", "4.63", "4.64", "4.65", "4.66", "4.67", "4.68", "4.69", "4.7", "4.71", "4.72", "4.73", "4.74", "4.75", "4.76", "4.77", "4.78", "4.79", "4.8", "4.81", "4.82", "4.83", "4.84", "4.85", "4.86", "4.87", "4.88", "4.89", "4.9", "4.91", "4.92", "4.93", "4.94", "4.95", "4.96", "4.97", "4.98", "4.99", "5.0", "5.01", "5.02", "5.03", "5.04", "5.05", "5.06", "5.07", "5.08", "5.09", "5.1", "5.11", "5.12", "5.13", "5.14", "5.15", "5.16", "5.17", "5.18", "5.19", "5.2", "5.21", "5.22", "5.23", "5.24", "5.25", "5.26", "5.27", "5.28", "5.29", "5.3", "5.31", "5.32", "5.33", "5.34", "5.35", "5.36", "5.37", "5.38", "5.39", "5.4", "5.41", "5.42", "5.43", "5.44", "5.45", "5.46", "5.47", "5.48", "5.49", "5.5", "5.51", "5.52", "5.53", "5.54", "5.55", "5.56", "5.57", "5.58", "5.59", "5.6", "5.61", "5.62", "5.63", "5.64", "5.65", "5.66", "5.67", "5.68", "5.69", "5.7", "5.71", "5.72", "5.73", "5.74", "5.75", "5.76", "5.77", "5.78", "5.79", "5.8", "5.81", "5.82", "5.83", "5.84", "5.85", "5.86", "5.87", "5.88", "5.89", "5.9", "5.91", "5.92", "5.93", "5.94", "5.95", "5.96", "5.97", "5.98", "5.99", "6.0"], "normal": ["0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "12.212327", "23.12695", "32.737123", "41.107783", "48.318322", "54.456628", "59.616977", "63.895568", "67.387992", "70.186614", "72.380364", "74.051972", "75.27813", "76.128931", "76.667757", "76.951225", "77.029609", "76.946455", "76.739281", "76.4407", "76.078027", "75.673896", "75.246899", "74.812119", "74.381561", "73.9644", "73.567", "73.194834", "72.850537", "72.536492", "72.252722", "71.999547", "71.776205", "71.581072", "71.412414", "71.268367", "71.146867", "71.04576", "70.962886", "70.896137", "70.843497", "70.803068", "70.773087", "70.751938", "70.738158", "70.730433", "70.727601", "70.728642", "70.732672", "70.738933", "70.746783", "70.755685", "70.765196", "70.774959", "70.78469", "70.794167", "70.803226", "70.811746", "70.819649", "70.826886", "70.833436", "70.839299", "70.844488", "70.849033", "70.85297", "70.856342", "70.859194", "70.861577", "70.863537", "70.865123", "70.866381", "70.867354", "70.868082", "70.868602", "70.868949", "70.869153", "70.86924", "70.869234", "70.869156", "70.869024", "70.868852", "70.868654", "70.86844", "70.868218", "70.867995", "70.867776", "70.867566", "70.867367", "70.867182", "70.867011", "70.866856", "70.866716", "70.866592", "70.866482", "70.866386", "70.866303", "70.866231", "70.866171", "70.866121", "70.866079", "55.421611", "42.053778", "30.67885", "21.108823", "13.16645", "6.678024", "1.472092", "-2.129342", "-4.625902", "-6.41988", "-7.664087", "-8.454097", "-8.871161", "-8.987784", "-8.868081", "-8.567171", "-8.13221", "-7.604716", "-7.015475", "-6.393311", "-5.758145", "-5.130433", "-4.521751", "-3.941743", "-3.398834", "-2.898106", "-2.437752", "-2.025628", "-1.653567", "-1.329764", "-1.045107", "-0.797833", "-0.586277", "-0.409085", "-0.262652", "-0.142213", "-0.044931", "0.016142", "-0.006357", "-0.001224", "0.004036", "-0.00329", "0.00131", "0.000238", "-0.000818", "0.000672", "-0.00027", "-4.6e-05", "0.000166", "-0.000137", "5.6e-05", "9e-06", "-3.4e-05", "2.8e-05", "-1.1e-05", "-2e-06", "7e-06", "-6e-06", "2e-06", "0.0", "-1e-06", "1e-06", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "-0.0", "0.0", "0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "0.0", "-0.0", "0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "-0.0", "-0.0", "-0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "-0.0", "-0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "-15.366539", "-28.680175", "-40.027049", "-49.578559", "-57.50847", "-63.991428", "-69.196383", "-73.285217", "-76.409948", "-78.711664", "-80.35131", "-81.38691", "-81.931801", "-82.097511", "-81.963443", "-81.599496", "-81.06532", "-80.410999", "-79.678496", "-78.902253", "-78.110389", "-77.324629", "-76.562266", "-75.835685", "-75.154052", "-74.523314", "-73.94682", "-73.426372", "-72.961357", "-72.550656", "-72.191376", "-71.88101", "-71.615819", "-71.391892", "-71.205315", "-71.052187", "-70.928681", "-70.831123", "-70.756038", "-70.700193", "-70.660621", "-70.634633", "-70.61982", "-70.614046", "-70.615441", "-70.622383", "-70.633479", "-70.64755", "-70.663609", "-70.680841", "-70.698585", "-70.716311", "-70.733611", "-70.750174", "-70.765777", "-70.780266", "-70.79355", "-70.805584", "-70.816365", "-70.825918", "-70.834292", "-70.841552", "-70.847775", "-70.853047", "-70.857454", "-70.861086", "-70.86403", "-70.866369", "-70.868184", "-70.869547", "-70.870528", "-70.871189", "-70.871584", "-70.871765", "-70.871773", "-70.871648", "-70.871422", "-70.871123", "-70.870774", "-70.870394", "-70.87", "-70.869603", "-70.869214", "-70.86884", "-70.868487", "-70.868158", "-70.867856", "-70.867581", "-70.867335", "-70.867117", "-70.866925", "-70.866759", "-70.866617", "-70.866496", "-70.866396", "-70.866313", "-70.866246", "-70.866193", "-70.866152", "-70.866122", "-55.374086", "-42.017403", "-30.652141", "-21.090293", "-13.154477", "-6.671436", "-1.469625", "2.125706", "4.576195", "6.378251", "7.631582", "8.428066", "8.849755", "8.971185", "8.855908", "8.559238", "8.128565", "7.602345", "7.015097", "6.394266", "5.762338", "5.134589", "4.526261", "3.947579", "3.405126", "2.900969", "2.443473", "2.028344", "1.660626", "1.331849", "1.046879", "0.801853", "0.592489", "0.413336", "0.264614", "0.143026", "0.045261", "-0.016156", "0.006383", "0.001229", "-0.004037", "0.003265", "-0.001269", "-0.00027", "0.000827", "-0.00066", "0.000252", "5.9e-05", "-0.000169", "0.000134", "-5e-05", "-1.3e-05", "3.5e-05", "-2.7e-05", "1e-05", "3e-06", "-7e-06", "5e-06", "-2e-06", "-1e-06", "1e-06", "-1e-06", "0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "0.0", "-0.0", "-0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0"], "fault": ["0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "0.0", "-1.062909", "-1.851335", "-2.416481", "-2.770642", "-2.917112", "-2.870188", "-2.629867", "-2.210664", "-1.615156", "-0.858212", "0.055924", "0.379705", "0.747469", "1.284116", "1.979841", "2.825171", "3.816363", "4.942822", "6.19542", "7.568111", "9.050361", "10.635921", "12.316509", "14.082637", "15.927348", "17.841905", "19.819893", "21.853635", "23.934395", "26.055905", "28.210723", "30.392126", "32.592938", "34.807572", "37.028777", "39.251193", "41.469147", "43.676343", "45.867497", "48.038192", "50.18345", "52.298116", "54.378701", "56.420228", "58.41979", "60.373142", "62.277493", "64.129685", "65.92676", "67.666466", "69.346562", "70.964755", "72.519266", "74.008591", "75.43158", "76.787054", "78.074319", "79.292094", "80.44051", "81.519154", "82.528063", "83.466842", "84.336391", "85.136905", "85.868943", "86.533373", "87.131314", "87.66325", "88.131073", "88.535505", "88.878184", "89.160785", "89.384816", "89.551957", "89.664", "89.722815", "89.730338", "89.68856", "89.599522", "89.465305", "89.288013", "89.069752", "88.812666", "88.519116", "88.191333", "87.831167", "87.441076", "87.023217", "86.579782", "86.112684", "85.624354", "85.116829", "84.592112", "84.052284", "83.499441", "82.935267", "82.361805", "81.780884", "81.194556", "80.604202", "78.297712", "75.870145", "73.342824", "70.72375", "68.023582", "65.252327", "62.419737", "59.536088", "56.61065", "53.652539", "50.67082", "47.674383", "44.670918", "41.669274", "38.676716", "35.700909", "32.748464", "29.827024", "26.9421", "24.099661", "21.306426", "18.566679", "15.88663", "13.269605", "10.720314", "8.243221", "5.841799", "3.518875", "1.278215", "-0.625184", "-1.839285", "-3.001292", "-4.110565", "-5.165095", "-6.165009", "-7.108675", "-7.999178", "-8.832887", "-9.611006", "-10.333966", "-11.001436", "-11.613928", "-12.173344", "-12.678334", "-13.130507", "-13.531546", "-13.881751", "-14.181963", "-14.433472", "-14.637492", "-14.795377", "-14.908969", "-14.979597", "-15.008581", "-14.997281", "-14.947159", "-14.859908", "-14.737854", "-14.582437", "-14.395205", "-14.177835", "-13.932049", "-13.659588", "-13.36229", "-13.042497", "-12.701191", "-12.339388", "-11.960661", "-11.564958", "-11.155482", "-10.731735", "-10.297716", "-9.854029", "-9.401897", "-8.942802", "-8.478153", "-8.010047", "-7.540388", "-7.067508", "-6.597016", "-6.125662", "-5.657675", "-5.194018", "-4.734683", "-4.280645", "-3.832718", "-3.392543", "-2.96313", "-2.53977", "-2.128531", "-1.725336", "-1.336992", "-0.957937", "-0.590242", "-0.235615", "0.074618", "-0.053851", "0.015132", "0.01426", "-0.023569", "0.0169", "-0.004624", "-0.004604", "0.007463", "-0.005302", "0.001412", "0.001485", "-0.002363", "0.001663", "-0.000431", "-0.000479", "0.000748", "-0.000521", "0.000131", "0.000154", "-0.000237", "0.000163", "-4e-05", "-5e-05", "7.5e-05", "-5.1e-05", "1.2e-05", "1.6e-05", "-2.4e-05", "1.6e-05", "-4e-06", "-5e-06", "8e-06", "-5e-06", "1e-06", "2e-06", "-2e-06", "2e-06", "-0.0", "-1e-06", "1e-06", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "-0.0", "-0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "0.0", "-0.0", "-1.411647", "-2.92791", "-4.558295", "-6.298394", "-8.136725", "-10.067524", "-12.080371", "-14.166922", "-16.319594", "-18.529469", "-20.78871", "-23.090291", "-25.425144", "-27.786918", "-30.167289", "-32.532922", "-34.913947", "-37.315054", "-39.709421", "-42.090867", "-44.454254", "-46.793335", "-49.103765", "-51.380282", "-53.618265", "-55.813773", "-57.962233", "-60.059929", "-62.103854", "-64.090707", "-66.017135", "-67.881055", "-69.679744", "-71.411395", "-73.080327", "-74.708959", "-76.230926", "-77.674638", "-79.044051", "-80.338846", "-81.558293", "-82.702403", "-83.770701", "-84.763853", "-85.681955", "-86.525624", "-87.295766", "-87.992767", "-88.618281", "-89.17309", "-89.658572", "-90.076245", "-90.427668", "-90.714454", "-90.938368", "-91.101525", "-91.205785", "-91.252921", "-91.245272", "-91.18515", "-91.074459", "-90.915468", "-90.71077", "-90.462562", "-90.173118", "-89.844807", "-89.480022", "-89.081122", "-88.650448", "-88.190478", "-87.703676", "-87.191856", "-86.657967", "-86.103791", "-85.531611", "-84.943802", "-84.34249", "-83.729756", "-83.107374", "-82.477663", "-81.84234", "-81.203547", "-80.562807", "-79.921921", "-79.282539", "-78.646224", "-78.014903", "-77.389375", "-76.771697", "-76.162734", "-75.563799", "-74.976122", "-74.400707", "-73.838515", "-73.290946", "-72.758132", "-72.241609", "-71.741563", "-71.258716", "-70.793806", "-68.632482", "-66.370506", "-64.026725", "-61.607957", "-59.124166", "-56.583627", "-53.995903", "-51.368784", "-48.711386", "-46.031247", "-43.336079", "-40.634202", "-37.932344", "-35.237657", "-32.556909", "-29.896834", "-27.26282", "-24.66097", "-22.096927", "-19.576247", "-17.103003", "-14.681693", "-12.317793", "-10.01334", "-7.773514", "-5.602357", "-3.500728", "-1.470209", "0.384467", "1.390877", "2.358053", "3.285963", "4.165463", "5.001011", "5.79179", "6.535877", "7.232916", "7.885519", "8.491778", "9.051312", "9.565888", "10.035885", "10.461458", "10.842435", "11.180354", "11.477277", "11.731175", "11.946148", "12.12017", "12.257375", "12.35603", "12.419576", "12.448631", "12.442974", "12.406487", "12.338623", "12.241095", "12.116613", "11.964035", "11.787909", "11.586523", "11.364517", "11.120251", "10.857063", "10.576945", "10.279361", "9.966555", "9.640243", "9.301807", "8.952621", "8.59408", "8.227157", "7.851901", "7.472396", "7.087959", "6.699989", "6.309381", "5.919446", "5.527774", "5.136889", "4.748259", "4.362802", "3.981524", "3.605166", "3.231869", "2.865867", "2.509184", "2.155498", "1.814329", "1.478764", "1.152655", "0.838163", "0.534383", "0.24045", "-0.039621", "0.05069", "-0.031314", "0.004568", "0.01272", "-0.015933", "0.009637", "-0.001219", "-0.004111", "0.005004", "-0.002963", "0.000315", "0.001326", "-0.001571", "0.00091", "-7.8e-05", "-0.000427", "0.000493", "-0.000279", "1.8e-05", "0.000137", "-0.000155", "8.6e-05", "-4e-06", "-4.4e-05", "4.8e-05", "-2.6e-05", "0.0", "1.4e-05", "-1.5e-05", "8e-06", "0.0", "-5e-06", "5e-06", "-2e-06", "-0.0", "1e-06", "-2e-06", "1e-06", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0", "0.0", "-0.0", "-0.0", "0.0", "-0.0"]}], "lstm": [{"epoch": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "loss": [0.32697079678376517, 0.24918111781279245, 0.22649371286233266, 0.21455842951933543, 0.20183837165435156, 0.18548320869604745, 0.18417984614769617, 0.1718385112285614, 0.1656652977069219, 0.15850804646809896, 0.15111509362856548, 0.14962053646643955, 0.13450375854969024, 0.12890492538611095, 0.11628341699639956, 0.11262778520584106, 0.09529240260521571, 0.0993493901193142, 0.07921913733084997, 0.06504939801990986, 0.06711372929314773, 0.05532834547261397, 0.047159572107096516, 0.04078819418946902, 0.04206077886124452, 0.04558041352157791, 0.021276149430001775, 0.026494858522589006, 0.02421362286278357, 0.029941392727196217, 0.018653526202154658, 0.018226392646320165, 0.011444563396119822, 0.008642312303806344, 0.011101467295860252, 0.006686252625077032, 0.005162821089130982, 0.004328247857629321, 0.0010990950346846755, 0.004105406678427244], "accuracy": [0.83947915, 0.8905208, 0.9039583, 0.90260416, 0.9065625, 0.9145833, 0.91489583, 0.91791666, 0.9245833, 0.92364585, 0.9273958, 0.92822915, 0.9405208, 0.9405208, 0.9479167, 0.9484375, 0.9585417, 0.95645833, 0.964375, 0.97260416, 0.97010416, 0.9788542, 0.98020834, 0.985, 0.98291665, 0.9820833, 0.991875, 0.99083334, 0.99114585, 0.98885417, 0.9919792, 0.99385417, 0.99625, 0.99635416, 0.9965625, 0.9975, 0.99833333, 0.99885416, 1.0, 0.99895835]}], "数据": [{"paraName": "数据集每段长度", "paraValue": "40988", "row_index": 0, "index": 0}, {"paraName": "数据集划分数量", "paraValue": "1000", "row_index": 1, "index": 1}, {"paraName": "数据集划分比例", "paraValue": "8：1：1", "row_index": 2, "index": 2}, {"paraName": "时间序列长度", "paraValue": "160", "row_index": 3, "index": 3}, {"paraName": "神经元个数", "paraValue": "128", "row_index": 4, "index": 4}, {"paraName": "训练次数", "paraValue": "40", "row_index": 5, "index": 5}], "tableData": [{"paras": "几方战步专。", "value": 182, "row_index": 0, "index": 0}, {"paras": "问千专声社将联。", "value": 56, "row_index": 1, "index": 1}, {"paras": "低集平任去加三率。", "value": 102, "row_index": 2, "index": 2}, {"paras": "都速该越还。", "value": 56, "row_index": 3, "index": 3}, {"paras": "持江被公连温验。", "value": 123, "row_index": 4, "index": 4}, {"paras": "石给别农说始。", "value": 58, "row_index": 5, "index": 5}, {"paras": "量节说时住话。", "value": 79, "row_index": 6, "index": 6}, {"paras": "同表名力部。", "value": 59, "row_index": 7, "index": 7}, {"paras": "片路济正专则了报。", "value": 115, "row_index": 8, "index": 8}, {"paras": "特老后花作儿。", "value": 55, "row_index": 9, "index": 9}], "tableData1": [{"time": "17:01", "duration": 19, "result": 0, "index": 0}, {"time": "17:04", "duration": 12, "result": 1, "index": 1}, {"time": "17:06", "duration": 21, "result": 2, "index": 2}, {"time": "17:09", "duration": 26, "result": 3, "index": 3}, {"time": "17:11", "duration": 27, "result": 0, "index": 4}, {"time": "17:21", "duration": 28, "result": 4, "index": 5}, {"time": "17:31", "duration": 12, "result": 5, "index": 6}, {"time": "17:41", "duration": 14, "result": 0, "index": 7}], "emaTreeData": [{"name": "机电伺服系统失效", "children": [{"name": "永磁同步电机故障", "children": [{"name": "定子故障", "children": [{"name": "元件开路", "children": [{"name": "绕组开路", "children": [{"name": "A绕组开路", "children": []}, {"name": "B绕组开路", "children": []}, {"name": "C绕组开路", "children": []}]}]}, {"name": "元件短路", "children": [{"name": "匝间短路", "children": [{"name": "A相匝间短路", "children": []}, {"name": "B相匝间短路", "children": []}, {"name": "C相匝间短路", "children": []}]}, {"name": "相间短路", "children": [{"name": "AB相间短路", "children": []}, {"name": "BC相间短路", "children": []}, {"name": "AC相间短路", "children": []}]}]}]}, {"name": "转子故障", "children": [{"name": "永磁体退磁失磁", "children": []}, {"name": "机械破损", "children": []}]}, {"name": "转轴故障", "children": [{"name": "转子偏心", "children": []}, {"name": "轴承故障", "children": []}]}]}, {"name": "行星滚柱丝杠故障", "children": [{"name": "表面损伤", "children": [{"name": "解除疲劳", "children": []}, {"name": "磨损", "children": []}, {"name": "点蚀", "children": []}]}, {"name": "丝杠变形断裂", "children": []}]}, {"name": "控制系统故障", "children": [{"name": "伺服控制器故障", "children": [{"name": "硬件故障", "children": [{"name": "AD转换模块损坏", "children": []}, {"name": "控制器主芯片损坏", "children": []}, {"name": "通信模块故障", "children": []}]}, {"name": "软件故障", "children": [{"name": "程序跑飞", "children": []}, {"name": "算法效率低", "children": []}]}]}, {"name": "电机驱动器故障", "children": [{"name": "高压供电异常", "children": []}, {"name": "IGBT短路", "children": [{"name": "A相上桥臂IGBT开路", "children": []}, {"name": "A相下桥臂IGBT开路", "children": []}, {"name": "B相上桥臂IGBT开路", "children": []}, {"name": "B相下桥臂IGBT开路", "children": []}, {"name": "C相上桥臂IGBT开路", "children": []}, {"name": "C相下桥臂IGBT开路", "children": []}]}, {"name": "IGBT开路", "children": []}]}, {"name": "传感器故障", "children": [{"name": "电流传感器故障", "children": [{"name": "开路故障", "children": []}, {"name": "偏置、漂移故障", "children": []}]}, {"name": "角度传感器故障", "children": [{"name": "开路故障", "children": []}, {"name": "偏置、漂移故障", "children": []}]}, {"name": "位移传感器故障", "children": [{"name": "开路故障", "children": []}, {"name": "偏置、漂移故障", "children": []}]}]}]}]}], "pumpTreeData": [{"name": "航空柱塞泵失效", "children": [{"name": "配油盘磨损故障", "children": []}, {"name": "入口压力不足故障", "children": []}, {"name": "轴承磨损故障", "children": []}, {"name": "柱塞球头间隙增大故障", "children": []}]}]}