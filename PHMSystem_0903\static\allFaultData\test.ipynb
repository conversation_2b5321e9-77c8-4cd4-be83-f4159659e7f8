{"cells": [{"metadata": {"ExecuteTime": {"end_time": "2025-02-26T09:48:42.685445Z", "start_time": "2025-02-26T09:48:42.567882Z"}}, "cell_type": "code", "source": ["import os\n", "import numpy as np\n", "\n", "# 定义目录路径\n", "dir2 = r'H:\\hyxd\\HYxingdong\\PHM\\PHMSystem_0903\\static\\allFaultData'\n", "\n", "# 定义文件名\n", "filename = 'EMAsvpwm2020_A_zajianduanlu-fre-0.3'\n", "\n", "# 拼接完整路径\n", "file_path = os.path.join(dir2, filename + '.npz')\n", "\n", "# 加载 .npz 文件\n", "data = np.load(file_path)\n", "\n", "# 打印 .npz 文件中的数组名称\n", "print(\"Arrays in the .npz file:\", data.files)\n", "\n", "# 打印每个数组的内容\n", "for array_name in data.files:\n", "    print(f\"Array '{array_name}':\")\n", "    print(data[array_name])\n"], "id": "d2acd34dfbc832e3", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Arrays in the .npz file: ['data', 'label']\n", "Array 'data':\n", "[[ 0.00000000e+00  1.00000000e-03  2.00000000e-03 ...  2.99800000e+00\n", "   2.99900000e+00  3.00000000e+00]\n", " [ 0.00000000e+00  3.76990895e-03  7.53980451e-03 ... -1.18166198e+00\n", "  -1.17861834e+00 -1.17557050e+00]\n", " [ 0.00000000e+00  2.22586728e-06  2.60324824e-05 ... -1.31759121e+00\n", "  -1.31472767e+00 -1.31185875e+00]\n", " [ 0.00000000e+00  1.28522164e-01 -3.92460995e-01 ... -1.26086212e-01\n", "   2.49447847e-01  2.46550988e-01]\n", " [ 0.00000000e+00 -1.99102541e-01  2.71996142e-01 ...  2.77008400e-01\n", "  -2.86543287e-01 -2.68846418e-01]\n", " [ 0.00000000e+00  1.02710918e-01  2.23496045e-02 ... -1.82443742e-01\n", "   9.94574017e-02  8.39331763e-02]]\n", "Array 'label':\n", "[16]\n"]}], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-02-26T10:05:13.895879Z", "start_time": "2025-02-26T10:05:13.889936Z"}}, "cell_type": "code", "source": "data['data']", "id": "c4a79cb31e684133", "outputs": [{"data": {"text/plain": ["array([[ 0.00000000e+00,  1.00000000e-03,  2.00000000e-03, ...,\n", "         2.99800000e+00,  2.99900000e+00,  3.00000000e+00],\n", "       [ 0.00000000e+00,  3.76990895e-03,  7.53980451e-03, ...,\n", "        -1.18166198e+00, -1.17861834e+00, -1.17557050e+00],\n", "       [ 0.00000000e+00,  2.22586728e-06,  2.60324824e-05, ...,\n", "        -1.31759121e+00, -1.31472767e+00, -1.31185875e+00],\n", "       [ 0.00000000e+00,  1.28522164e-01, -3.92460995e-01, ...,\n", "        -1.26086212e-01,  2.49447847e-01,  2.46550988e-01],\n", "       [ 0.00000000e+00, -1.99102541e-01,  2.71996142e-01, ...,\n", "         2.77008400e-01, -2.86543287e-01, -2.68846418e-01],\n", "       [ 0.00000000e+00,  1.02710918e-01,  2.23496045e-02, ...,\n", "        -1.82443742e-01,  9.94574017e-02,  8.39331763e-02]])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "execution_count": 4}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "8d90e1f75d35a1a"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "f771946c326847c4"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "e07e6768333fcf1a"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "2579e9f0a6dc7a3e"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "446dc9c3b608d837"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "9628ee9b0d0293f"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}