
82e2ded2dd384c5446ee2bee4d2511ac40dfbaf4	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"ac4b4dac0a719291c1a92d1e5952ee4e\"}","integrity":"sha512-G7siw5ncR42cWFKfb0nzNgledUMw1VH+CAZjL3KqUFlZiWyB7UvujL1NGGbOrxdW891AiexG4ZJQsum4EHRQhg==","time":1753796984800,"size":245849}