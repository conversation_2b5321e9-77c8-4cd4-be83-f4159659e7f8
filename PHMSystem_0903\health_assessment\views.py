from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from rest_framework.decorators import api_view
from rest_framework.response import Response
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from .health_algorithm import HealthAssessmentAlgorithm, RULPredictionAlgorithm
from .report_generator import ReportGenerator
from .models import HealthAssessment, ComponentHealth, RULPrediction

# 辅助函数 - 根据故障模式获取对应的组件
def get_component_from_fault_mode(fault_mode):
    """
    从故障模式标识中识别对应的组件
    
    参数:
        fault_mode: 故障模式标识字符串，如'1_fault_motor_open'
        
    返回:
        组件名称: 如'电机'
    """
    component_mapping = {
        'magnet': '电机',
        'brush': '电机',
        'commutator': '电机',
        'stator': '电机',
        'rotor': '电机',
        'bearing': '减速器',
        'gear': '减速器',
        'sensor': '传感器',
        'mosfet': '控制器',
        'drive': '控制器',
        'mcu': '控制器'
    }
    
    # 遍历映射表，检查故障模式中是否包含关键词
    for key, component in component_mapping.items():
        if key in fault_mode.lower():
            return component
    
    # 默认返回电机
    return '电机'

# 辅助函数 - 生成有故障的趋势数据
def get_trend_data_with_fault(days=30):
    """
    生成有故障发生的健康度趋势数据
    
    参数:
        days: 生成多少天的数据
        
    返回:
        趋势数据列表: [(日期, 健康度), ...]
    """
    trend_data = []
    today = datetime.now()
    
    # 生成前25天的正常数据，健康度在80-95之间
    for i in range(days-5, -1, -1):
        date = today - timedelta(days=i)
        date_str = date.strftime('%Y-%m-%d')
        
        if i > 5:  # 前20天比较稳定
            health = 85 + np.random.normal(0, 2)
        else:  # 最后5天开始下降
            health = max(0, 85 - (5-i) * 20 + np.random.normal(0, 2))
            
        trend_data.append([date_str, round(health, 1)])
    
    return trend_data

# 辅助函数 - 生成有退化的趋势数据
def get_trend_data_with_degradation(days=30, component=None):
    """
    生成有退化趋势的健康度数据
    
    参数:
        days: 生成多少天的数据
        component: 退化的组件名称
        
    返回:
        趋势数据列表: [(日期, 健康度), ...]
    """
    trend_data = []
    today = datetime.now()
    
    # 生成30天的数据，健康度缓慢下降
    for i in range(days, -1, -1):
        date = today - timedelta(days=i)
        date_str = date.strftime('%Y-%m-%d')
        
        # 健康度从90缓慢下降到75
        base_health = 90 - (days-i) * 15 / days
        random_factor = np.random.normal(0, 1.5)
        health = max(0, min(100, base_health + random_factor))
        
        trend_data.append([date_str, round(health, 1)])
    
    return trend_data

# 辅助函数 - 生成正常的趋势数据
def get_trend_data_normal(days=30):
    """
    生成正常状态的健康度趋势数据
    
    参数:
        days: 生成多少天的数据
        
    返回:
        趋势数据列表: [(日期, 健康度), ...]
    """
    trend_data = []
    today = datetime.now()
    
    # 生成30天的数据，健康度维持在高水平
    for i in range(days, -1, -1):
        date = today - timedelta(days=i)
        date_str = date.strftime('%Y-%m-%d')
        
        # 健康度在85-95之间波动
        health = 90 + np.random.normal(0, 2)
        health = max(85, min(95, health))
        
        trend_data.append([date_str, round(health, 1)])
    
    return trend_data

@api_view(['GET'])
def api_test(request):
    """
    简单的测试接口
    """
    return Response({
        'code': 200,
        'message': 'API测试成功',
        'data': {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    })

@api_view(['GET'])
def get_health_prediction(request):
    """
    获取健康评估和寿命预测数据
    """
    try:
        # 获取请求参数
        reset_data = request.query_params.get('reset', 'false').lower() == 'true'
        fault_mode = request.query_params.get('fault_mode', None)
        
        # 如果是重置请求，返回初始状态数据
        if reset_data:
            return Response({
                'code': 200,
                'message': '数据已重置',
                'data': {
                    'current_health': 0,
                    'health_status': 'danger',
                    'component_health': [
                        {'name': '电机', 'value': 0},
                        {'name': '控制器', 'value': 0},
                        {'name': '减速器', 'value': 0},
                        {'name': '传感器', 'value': 0}
                    ],
                    'rul_hours': 0,
                    'rul_percentage': 0,
                    'health_trend': []
                }
            })
        
        # 如果有故障诊断结果，根据结果设置健康状态
        if fault_mode:
            # 处理故障诊断结果
            if 'fault' in fault_mode:  # 严重故障
                faulty_component = get_component_from_fault_mode(fault_mode)
                
                # 创建部件健康状态数据
                component_health = []
                for component in ['电机', '控制器', '减速器', '传感器']:
                    if component == faulty_component:
                        component_health.append({'name': component, 'value': 0})  # 故障部件健康度为0
                    else:
                        component_health.append({'name': component, 'value': 85})  # 其他部件正常
                
                return Response({
                    'code': 200,
                    'message': '基于故障诊断的健康评估',
                    'data': {
                        'current_health': 0,  # 严重故障时系统健康度为0
                        'health_status': 'danger',
                        'component_health': component_health,
                        'rul_hours': 0,  # 严重故障时剩余寿命为0
                        'rul_percentage': 0,
                        'health_trend': get_trend_data_with_fault(30)
                    }
                })
            elif 'degradation' in fault_mode:  # 退化情况
                degraded_component = get_component_from_fault_mode(fault_mode)
                
                # 创建部件健康状态数据
                component_health = []
                overall_health = 75  # 退化情况下系统健康度降低
                
                for component in ['电机', '控制器', '减速器', '传感器']:
                    if component == degraded_component:
                        component_health.append({'name': component, 'value': 50})  # 退化部件健康度降低
                    else:
                        component_health.append({'name': component, 'value': 85})  # 其他部件正常
                
                # 获取健康评估算法实例
                health_algorithm = HealthAssessmentAlgorithm()
                
                # 计算加权健康度
                component_health_dict = {item['name']: item['value'] for item in component_health}
                total_weight = sum(health_algorithm.component_weights.values())
                weighted_sum = sum(component_health_dict.get(comp, 85) * health_algorithm.component_weights[comp] 
                                for comp in health_algorithm.component_weights)
                
                overall_health = weighted_sum / total_weight if total_weight > 0 else 75
                
                # 预测RUL
                rul_algorithm = RULPredictionAlgorithm()
                rul_result = rul_algorithm.predict_rul(None, overall_health)
                
                return Response({
                    'code': 200,
                    'message': '基于退化状态的健康评估',
                    'data': {
                        'current_health': overall_health,
                        'health_status': 'warning',
                        'component_health': component_health,
                        'rul_hours': round(rul_result['remaining_hours']),
                        'rul_percentage': round(rul_result['life_percentage']),
                        'health_trend': get_trend_data_with_degradation(30, degraded_component)
                    }
                })
            else:  # 正常状态
                component_health = [
                    {'name': '电机', 'value': 90},
                    {'name': '控制器', 'value': 88},
                    {'name': '减速器', 'value': 92},
                    {'name': '传感器', 'value': 95}
                ]
                
                return Response({
                    'code': 200,
                    'message': '正常状态的健康评估',
                    'data': {
                        'current_health': 91,  # 正常状态下系统健康度高
                        'health_status': 'good',
                        'component_health': component_health,
                        'rul_hours': 9100,  # 正常状态下剩余寿命较长，已经是整数
                        'rul_percentage': 91,
                        'health_trend': get_trend_data_normal(30)
                    }
                })
        
        # 正常健康评估流程（没有故障诊断结果）
        # 获取传感器数据
        sensor_data = {
            'motor_temp': 58.5,
            'motor_vibration': 0.42,
            'pressure': 5.8,
            'current': 2.3
        }
        
        # 健康评估
        health_algorithm = HealthAssessmentAlgorithm()
        health_result = health_algorithm.evaluate_health(sensor_data)
        
        # 寿命预测
        rul_algorithm = RULPredictionAlgorithm()
        
        # 假设已有历史数据用于特征提取
        history_data = pd.DataFrame({
            'vibration': [0.3 + 0.01 * i for i in range(10)],
            'temperature': [50 + 0.5 * i for i in range(10)],
            'pressure': [5.5 + 0.1 * i for i in range(10)],
            'current': [2.0 + 0.05 * i for i in range(10)]
        })
        
        # 当前数据
        current_data = pd.DataFrame({
            'vibration': [sensor_data['motor_vibration']],
            'temperature': [sensor_data['motor_temp']],
            'pressure': [sensor_data['pressure']],
            'current': [sensor_data['current']]
        })
        
        # 提取特征
        features = rul_algorithm.prepare_features(history_data, current_data)
        
        # 预测寿命
        rul_result = rul_algorithm.predict_rul(features, health_result['health_score'])
        
        # 获取健康度趋势
        trend_data = rul_algorithm.get_health_trend(days=30)
        
        # 保存评估结果到数据库
        try:
            # 在实际应用中，将评估结果保存到数据库供后续查询
            health_record = HealthAssessment.objects.create(
                health_score=health_result['health_score'],
                health_status=health_result['health_status'],
                remarks='自动评估'
            )
            
            # 保存部件健康记录
            for component, score in health_result['component_health'].items():
                ComponentHealth.objects.create(
                    assessment=health_record,
                    component_name=component,
                    health_score=score
                )
            
            # 保存寿命预测记录
            RULPrediction.objects.create(
                assessment=health_record,
                remaining_hours=rul_result['remaining_hours'],
                life_percentage=rul_result['life_percentage'],
                confidence=rul_result['confidence']
            )
        except Exception as db_error:
            print(f"保存数据库记录失败: {str(db_error)}")
            # 不阻止API继续执行
        
        # 返回结果
        return Response({
            'code': 200,
            'message': '获取健康评估和寿命预测数据成功',
            'data': {
                'current_health': health_result['health_score'],
                'health_status': health_result['health_status'],
                'component_health': [
                    {'name': k, 'value': v} for k, v in health_result['component_health'].items()
                ],
                'rul_hours': round(rul_result['remaining_hours']),
                'rul_percentage': round(rul_result['life_percentage']),
                'health_trend': trend_data
            }
        })
    
    except Exception as e:
        import traceback
        print(f"健康评估异常: {str(e)}")
        print(traceback.format_exc())
        return Response({
            'code': 500,
            'message': f'健康评估失败: {str(e)}',
            'data': {}
        })

@api_view(['POST'])
def generate_health_report(request):
    """
    生成健康评估报告
    """
    try:
        # 解析请求数据
        request_data = request.data.get('health_data', {})
        
        # 构建评估数据
        assessment_data = {
            'device_id': 'SYS-001',  # 设备编号
            'health_score': request_data.get('current_health', 0),
            'health_status': 'good' if request_data.get('current_health', 0) >= 80 else 
                            'warning' if request_data.get('current_health', 0) >= 60 else 'danger',
            'remaining_hours': request_data.get('rul_hours', 0),
            'life_percentage': request_data.get('rul_percentage', 0),
            'confidence': 90.0,
            'component_health': {item['name']: item['value'] for item in request_data.get('component_health', [])}
        }
        
        # 获取健康度趋势数据
        rul_algorithm = RULPredictionAlgorithm()
        trend_data = rul_algorithm.get_health_trend(days=30)
        
        # 生成报告
        report_generator = ReportGenerator()
        report_bytes = report_generator.generate_report(assessment_data, trend_data)
        
        # 返回报告文件
        response = HttpResponse(
            report_bytes,
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
        # 设置文件名
        now = datetime.now()
        filename = f"健康评估报告_{now.strftime('%Y%m%d%H%M%S')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        
        return response
    
    except Exception as e:
        import traceback
        print(f"生成报告异常: {str(e)}")
        print(traceback.format_exc())
        return Response({
            'code': 500,
            'message': f'生成报告失败: {str(e)}',
            'data': {}
        })
