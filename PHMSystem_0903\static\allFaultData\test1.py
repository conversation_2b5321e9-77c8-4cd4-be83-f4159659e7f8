import os
import numpy as np

# 定义目录路径
dir2 = r'H:\hyxd\HYxingdong\PHM\PHMSystem_0903\static\allFaultData'

# 定义文件名
filename = 'EMAsvpwm2020_shici-fre-3.6'

# 拼接完整路径
file_path = os.path.join(dir2, filename + '.npz')

# 加载 .npz 文件
data = np.load(file_path)

# 打印 .npz 文件中的数组名称
print("Arrays in the .npz file:", data.files)

# 打印每个数组的内容
for array_name in data.files:
    print(f"Array '{array_name}':")
    print(data[array_name])

len(data['data'])