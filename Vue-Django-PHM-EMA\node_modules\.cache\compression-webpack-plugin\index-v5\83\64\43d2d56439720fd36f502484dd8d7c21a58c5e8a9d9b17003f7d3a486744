
124bbb92eab0d63581c43378837d9ae75ab908c6	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"dd9c24aa6487e54ccf50ebb6dca9a1e3\"}","integrity":"sha512-eXXEAlamn999ooO0x9Be+zH50ko9N+E6hhmEl81bW3NELRL72ZOVlzdVsWMhTdNu10xE6bfks8xhDjRN5CaAWg==","time":1753796984030,"size":26763}