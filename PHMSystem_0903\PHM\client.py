import requests,json,random,os,time,datetime, xlrd
import ast

def login():
    targetURL = "http://127.0.0.1:8000/user/login/"
    postData = {"username":"<EMAIL>","password":"123456"}
    postData = json.dumps(postData)
    myHeader = {"user-agent":"Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3947.100 Safari/537.36"}

    req = requests.post(url = targetURL,data = postData,headers=myHeader)
    #req = requests.get(url = targetURL,headers=myHeader)

    print(req.text)
def signUp():
    targetURL = "http://127.0.0.1:8000/user/signUp/"
    postData = {"nickname": "sunsun","email":"<EMAIL>","password":"123456",
                "phone":"15764324892","gender":"0","info":""
                }
    postData = json.dumps(postData)
    myHeader = {
        "user-agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3947.100 Safari/537.36"}

    req = requests.post(url=targetURL, data=postData, headers=myHeader)
    # req = requests.get(url = targetURL,headers=myHeader)
    print(req.text)

def logout(token):
    targetURL = "http://127.0.0.1:8000/user/logout/"
    postData = {"token": token,"username":"<EMAIL>"}
    postData = json.dumps(postData)
    myHeader = {
        "user-agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3947.100 Safari/537.36"}

    req = requests.post(url=targetURL, data=postData, headers=myHeader)
    # req = requests.get(url = targetURL,headers=myHeader)
    print(req.text)
def getToken():
    randomNum = random.randint(100000000,1000000000)
    return str(randomNum)

def emailVerify():
    targetURL = "http://127.0.0.1:8000/user/emailVerify?email=<EMAIL>"
    myHeader = {
        "user-agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3947.100 Safari/537.36"}
    req = requests.get(url=targetURL, headers=myHeader)
    # req = requests.get(url = targetURL,headers=myHeader)
    print(req.text)

def emailVerifyPost(email,flag):
    targetURL = "http://127.0.0.1:8000/user/emailVerify/"
    myHeader = {
        "user-agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3947.100 Safari/537.36"}
    postData = {"email": email,"password":flag}
    #postData = json.dumps(postData)
    req = requests.post(url=targetURL, headers=myHeader, data = postData)
    # req = requests.get(url = targetURL,headers=myHeader)
    print(req.text)

def changePassword(token):
    """
    修改密码
    :param token:
    :return:
    """
    targetURL = "http://127.0.0.1:8000/user/changePassword/"
    postData = {"token": token, "username": "<EMAIL>","newPassword":"123456"}
    postData = json.dumps(postData)
    myHeader = {
        "user-agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3947.100 Safari/537.36"}
    req = requests.post(url=targetURL, data=postData, headers=myHeader)
    # req = requests.get(url = targetURL,headers=myHeader)
    print(req.text)

def changeInfo(token):
    """
    修改密码
    :param token:
    :return:
    """
    targetURL = "http://127.0.0.1:8000/user/changeInfo/"
    postData = {"token": token, "username": "<EMAIL>","nickname":"sun44sun",
                'info':"我是孙孙","gender":"1","phone":"15764324892","qq":"1450246370",
                "wechat":"szy12345"
                }
    postData = json.dumps(postData)
    myHeader = {
        "user-agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3947.100 Safari/537.36"}
    req = requests.post(url=targetURL, data=postData, headers=myHeader)
    # req = requests.get(url = targetURL,headers=myHeader)
    print(req.text)

def uploadIcon(token,iconPath):
    """
    修改密码
    :param token:
    :return:
    """
    targetURL = "http://127.0.0.1:8000/user/uploadIcon/"
    postData = {"token": token, "username": "<EMAIL>"}
    #postData = json.dumps(postData)
    myHeader = {
        "user-agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3947.100 Safari/537.36"}
    fl = open(iconPath, 'rb')
    files = {'upload_icon_file': (
    'head_icon_default.jpg', fl, 'application/octet-stream', {'Expires': '0'})}  # 字段名files 以及类型和application/octet-stream 和抓取到的接口一致
    req = requests.post(url = targetURL, data = postData, files = files)
    #print(requests.Request('POST', targetURL, headers=myHeader, files=files).prepare().body.decode('ascii'))
    print(req.text)
def uploadData():
    X = random.randint(-30,50)
    Y= random.randint(-30,50)
    Z = random.randint(-30,50)
    i = random.randint(0,len(load_force))
    # targetURL = "http://42.193.99.46:8000/phm/upData/"
    targetURL = "http://127.0.0.1:8000/phm/upData/"
    postData = {"time":str(datetime.datetime.now()),
                "X":str(X),
                "Y":str(Y),
                "Z":str(Z),
                # "load_force":str(random.randint(0,2000)),
                "load_force":  load_force[i],
                "line_displacement": line_displacement[i],
                "line_speed": line_speed[i]
                }
    postData = json.dumps(postData)
    print(postData)
    myHeader = {
        "user-agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3947.100 Safari/537.36"}

    req = requests.post(url=targetURL, data=postData, headers=myHeader)
    # req = requests.get(url = targetURL,headers=myHeader)
    code = eval(req.text)
    # print(code,type(code))
    # print(req.text['code'])
    if code['code'] == 200:
        print('成功发送数据')
def faultDiagnosis(data):
    """
    修改密码
    :param token:
    :return:
    """
    targetURL = "http://42.193.99.46:8000/phm/faultDiagnosis/"
    postData = {"X":data[0],"Y":data[1],"Z":data[2]}
    print(postData)
    postData = json.dumps(postData)
    myHeader = {
        "user-agent": "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3947.100 Safari/537.36"}
    req = requests.post(url = targetURL, data = postData, headers = myHeader)
    print(req.text,type(req.text))
    #print(requests.Request('POST', targetURL, headers=myHeader, files=files).prepare().body.decode('ascii'))
def faultD():
    data = []
    x1 = ""
    y1 = ""
    z1 = ""
    for i in range(511):
        x1 += str(random.uniform(-30, 50)) + ","
        y1 += str(random.uniform(-30, 50)) + ","
        z1 += str(random.uniform(-30, 50)) + ","
    x1 += str(random.uniform(-30, 50))
    y1 += str(random.uniform(-30, 50))
    z1 += str(random.uniform(-30, 50))
    data.append(x1)
    data.append(y1)
    data.append(z1)
    print(data)
    faultDiagnosis(data)
def read_excel():
    global load_force, line_displacement, line_speed
    workbook = xlrd.open_workbook(r'C:\2-0.1-0-25-0.0005.xlsx')
    sheet1_name = workbook.sheet_names()[0]
    sheet1 = workbook.sheet_by_name(sheet1_name)
    load_force = sheet1.col_values(3)[1:]
    line_displacement = sheet1.col_values(11)[1:]
    line_speed = sheet1.col_values(7)[1:]

if __name__ == "__main__":
    #login()
    #logout(118596485)
    #signUp()
    #emailVerify()
    #emailVerifyPost( "<EMAIL>",'0')
    #changePassword(694867208)
    #changeInfo(823305929)
    #uploadIcon("435279654","C:\\Users\\<USER>\\Pictures\\Saved Pictures\\youyi.jpg")
    #faultD()
    read_excel()
    num = 0
    while True:
        try:
            uploadData()
            if num > 512:
                time.sleep(2)
            else:
                num += 1
                # time.sleep(0.01)
        except:
            print('未正常发送数据')

