# 设备状态监测功能开发日志

## 功能概述

设备状态监测模块（原"传感器监控"）提供了实时监控设备传感器数据的功能，包括：
- 选择不同传感器类型进行监测
- 动态显示传感器数据曲线图
- 调整采样频率（0.5Hz-20Hz）
- 数据下载功能
- 实时数据显示与历史数据对比

## 代码目录结构

### 前端代码
- **主视图组件**: `Vue-Django-PHM-EMA/src/views/sensor-monitor/index.vue`
- **图表组件**: `Vue-Django-PHM-EMA/src/views/sensor-monitor/components/SensorChart.vue`
- **API接口文件**: `Vue-Django-PHM-EMA/src/api/sensor.js`
- **路由配置**: `Vue-Django-PHM-EMA/src/router/index.js`

### 后端代码
- **视图文件**: `PHMSystem_0903/PHM/views.py`
- **URL配置**: `PHMSystem_0903/PHM/urls.py`

## 开发过程记录

1. **初始需求分析**
   - 确定需要实现的传感器类型：温度、压力、振动、位移
   - 确定数据显示方式：实时曲线图
   - 确定交互功能：开始/停止监控、数据下载

2. **前端开发**
   - 创建主视图组件，实现传感器选择、频率调整、监控控制等功能
   - 创建图表组件，使用Chart.js实现动态数据可视化
   - 实现数据下载功能，支持CSV格式导出

3. **后端开发**
   - 实现传感器数据生成API，支持不同类型传感器数据模拟
   - 添加采样频率参数支持，动态调整数据返回量
   - 实现数据缓存机制，确保数据连续性

4. **问题解决记录**

   a. **数据结构不匹配问题**
   - 问题：前端访问 `response.data.data.data` 而后端返回 `response.data.data`
   - 解决：修改前端代码，正确访问数据结构

   b. **采样频率选项扩展**
   - 增加了从0.5Hz到20Hz的多个频率选项
   - 根据频率动态调整数据获取量和更新间隔

   c. **指令位移传感器数据显示突变问题**
   - 问题：位移传感器数据在循环时出现不连续
   - 解决方案：
     - 保持数据文件一致性
     - 确保数据连续性
     - 改进噪声添加方式
     - 增加缓冲区大小
     - 优化数据循环方式

   d. **波形分析与优化**
   - 分析发现指令位移传感器数据是一个正弦波信号
   - 周期约为2000个采样点，幅值范围在-0.5到0.5之间
   - 修改代码确保波形的平滑连续性

5. **模块名称修改**
   - 将"传感器监控"模块名称修改为"设备状态监测"
   - 修改组件名：从`SensorMonitor`改为`DeviceStatusMonitor`
   - 修改路由配置中的名称和标题

## 主要功能说明

1. **传感器选择功能**
   - 支持选择不同类型的传感器：温度、压力、振动、位移
   - 每种传感器有不同的数据范围和显示样式

2. **采样频率调整**
   - 支持多种采样频率：0.5Hz, 1Hz, 2Hz, 5Hz, 10Hz, 20Hz
   - 频率越高，数据更新越频繁，曲线更平滑

3. **实时数据监控**
   - 动态显示传感器数据曲线
   - 显示最新数据值
   - 监控状态指示（运行中/已停止）

4. **数据下载功能**
   - 支持将当前监控的数据导出为CSV文件
   - 文件名包含传感器类型和时间戳

5. **数据缓冲机制**
   - 使用数据缓冲区存储历史数据
   - 保持数据连续性，避免波形突变
   - 优化内存使用，避免数据过多导致性能问题

## 技术实现要点

1. **数据生成与模拟**
   - 使用数学函数模拟不同传感器的数据特性
   - 添加随机噪声使数据更真实
   - 预生成大量数据点并循环使用，确保数据连续性

2. **前端性能优化**
   - 使用缓冲区管理数据，避免频繁请求
   - 图表渲染优化，禁用不必要的动画
   - 使用 'none' 模式更新图表，提高性能

3. **数据连续性保证**
   - 维护全局数据索引，确保数据连续
   - 使用数据缓冲区平滑处理数据变化
   - 特别处理位移传感器数据，避免波形突变

## 后续优化建议

1. **数据持久化**
   - 添加数据库存储功能，保存历史数据
   - 实现历史数据查询和对比分析功能

2. **告警功能**
   - 添加数据阈值设置
   - 当数据超出阈值时触发告警

3. **多传感器同时监控**
   - 支持在同一图表中显示多个传感器数据
   - 添加数据对比分析功能

4. **数据分析功能**
   - 添加数据统计分析功能
   - 实现趋势预测和异常检测

5. **移动端适配**
   - 优化界面布局，支持移动设备访问
   - 添加推送通知功能 