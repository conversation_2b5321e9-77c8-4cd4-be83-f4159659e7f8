import pandas as pd
import numpy as np
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.chart import LineChart, Reference, BarChart
from openpyxl.chart.axis import DateAxis
from openpyxl.drawing.image import Image
from openpyxl.utils import get_column_letter
import io
import os
from datetime import datetime

class ReportGenerator:
    """健康评估报告生成器"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.wb = None
        self.logo_path = None  # 可选：公司Logo路径
    
    def _init_workbook(self):
        """初始化工作簿"""
        self.wb = Workbook()
        # 删除默认的Sheet
        default_sheet = self.wb.active
        self.wb.remove(default_sheet)
    
    def _create_cover_sheet(self, assessment_data):
        """创建报告封面"""
        ws = self.wb.create_sheet(title="报告封面")
        
        # 设置列宽
        ws.column_dimensions['A'].width = 5
        ws.column_dimensions['B'].width = 25
        ws.column_dimensions['C'].width = 25
        ws.column_dimensions['D'].width = 25
        ws.column_dimensions['E'].width = 5
        
        # 添加公司Logo
        # if self.logo_path and os.path.exists(self.logo_path):
        #     img = Image(self.logo_path)
        #     ws.add_image(img, 'B2')
        
        # 标题
        ws['B6'] = "伺服系统健康评估报告"
        title_font = Font(name='微软雅黑', size=24, bold=True)
        ws['B6'].font = title_font
        ws['B6'].alignment = Alignment(horizontal='center')
        ws.merge_cells('B6:D6')
        
        # 报告基本信息
        ws['B8'] = "评估日期:"
        ws['C8'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        ws['B9'] = "设备编号:"
        ws['C9'] = assessment_data.get("device_id", "SYS-001")
        
        ws['B10'] = "当前健康度评分:"
        ws['C10'] = f"{assessment_data.get('health_score', 0):.1f}分"
        
        # 健康状态显示
        ws['B11'] = "健康状态:"
        health_status = assessment_data.get('health_status', 'unknown')
        status_text = {
            'good': '良好',
            'warning': '警告',
            'danger': '危险',
            'unknown': '未知'
        }.get(health_status, '未知')
        ws['C11'] = status_text
        
        # 设置健康状态单元格的背景颜色
        status_fill = {
            'good': PatternFill(start_color="92D050", end_color="92D050", fill_type="solid"),
            'warning': PatternFill(start_color="FFC000", end_color="FFC000", fill_type="solid"),
            'danger': PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid"),
            'unknown': PatternFill(start_color="BFBFBF", end_color="BFBFBF", fill_type="solid")
        }.get(health_status, PatternFill(start_color="BFBFBF", end_color="BFBFBF", fill_type="solid"))
        ws['C11'].fill = status_fill
        
        # 剩余使用寿命
        ws['B12'] = "预计剩余使用寿命:"
        ws['C12'] = f"{assessment_data.get('remaining_hours', 0):.1f}小时"
        
        # 评估描述
        ws['B14'] = "评估摘要:"
        ws['B15'] = self._generate_assessment_summary(assessment_data)
        ws['B15'].alignment = Alignment(wrap_text=True, vertical='top')
        ws.merge_cells('B15:D18')
        
        # 生成日期
        ws['B20'] = "报告生成日期:"
        ws['C20'] = datetime.now().strftime('%Y-%m-%d')
    
    def _generate_assessment_summary(self, assessment_data):
        """生成评估摘要文本"""
        health_score = assessment_data.get('health_score', 0)
        health_status = assessment_data.get('health_status', 'unknown')
        remaining_hours = assessment_data.get('remaining_hours', 0)
        component_health = assessment_data.get('component_health', {})
        
        status_text = {
            'good': '良好',
            'warning': '警告',
            'danger': '危险',
            'unknown': '未知'
        }.get(health_status, '未知')
        
        summary = f"本次评估结果显示，伺服系统当前整体健康度评分为{health_score:.1f}分，健康状态为{status_text}。"
        summary += f"预计剩余使用寿命约为{remaining_hours:.1f}小时。\n\n"
        
        # 添加部件健康状况描述
        if component_health:
            max_health_component = max(component_health.items(), key=lambda x: x[1])
            min_health_component = min(component_health.items(), key=lambda x: x[1])
            
            summary += f"各关键部件中，{max_health_component[0]}健康状况最佳（{max_health_component[1]:.1f}分），"
            summary += f"{min_health_component[0]}健康状况需关注（{min_health_component[1]:.1f}分）。"
            
            if min_health_component[1] < 60:
                summary += f"\n\n建议：请尽快检查{min_health_component[0]}的运行状态，必要时进行维修或更换。"
            elif min_health_component[1] < 80:
                summary += f"\n\n建议：请定期关注{min_health_component[0]}的运行状态，避免潜在故障风险。"
        
        return summary
    
    def _create_health_details_sheet(self, assessment_data):
        """创建健康详情页"""
        ws = self.wb.create_sheet(title="健康评估详情")
        
        # 设置列宽
        ws.column_dimensions['A'].width = 5
        ws.column_dimensions['B'].width = 15
        ws.column_dimensions['C'].width = 15
        ws.column_dimensions['D'].width = 15
        ws.column_dimensions['E'].width = 15
        ws.column_dimensions['F'].width = 15
        
        # 标题
        ws['B2'] = "伺服系统健康评估详情"
        title_font = Font(name='微软雅黑', size=16, bold=True)
        ws['B2'].font = title_font
        ws['B2'].alignment = Alignment(horizontal='center')
        ws.merge_cells('B2:F2')
        
        # 表头
        headers = ["部件名称", "健康度评分", "健康状态", "建议措施"]
        for i, header in enumerate(headers):
            cell = ws.cell(row=4, column=i+2)
            cell.value = header
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        
        # 部件健康数据
        component_health = assessment_data.get('component_health', {})
        row = 5
        for component, score in component_health.items():
            # 部件名称
            ws.cell(row=row, column=2).value = component
            
            # 健康度评分
            ws.cell(row=row, column=3).value = score
            
            # 健康状态
            if score >= 80:
                status = "良好"
                fill_color = "92D050"
            elif score >= 60:
                status = "警告"
                fill_color = "FFC000"
            else:
                status = "危险"
                fill_color = "FF0000"
            
            ws.cell(row=row, column=4).value = status
            ws.cell(row=row, column=4).fill = PatternFill(start_color=fill_color, end_color=fill_color, fill_type="solid")
            
            # 建议措施
            if score >= 80:
                recommendation = "定期维护"
            elif score >= 60:
                recommendation = "密切监控"
            else:
                recommendation = "尽快检修"
            
            ws.cell(row=row, column=5).value = recommendation
            
            row += 1
        
        # 添加健康度柱状图
        self._add_health_bar_chart(ws, component_health, row+2)
    
    def _add_health_bar_chart(self, ws, component_health, start_row):
        """添加健康度柱状图"""
        # 准备图表数据
        components = list(component_health.keys())
        scores = list(component_health.values())
        
        # 写入图表数据
        for i, component in enumerate(components):
            ws.cell(row=start_row+i, column=2).value = component
            ws.cell(row=start_row+i, column=3).value = scores[i]
        
        # 创建图表
        chart = BarChart()
        chart.title = "各部件健康度评分"
        chart.style = 10
        chart.x_axis.title = "部件名称"
        chart.y_axis.title = "健康度评分"
        chart.y_axis.scaling.min = 0
        chart.y_axis.scaling.max = 100
        
        # 设置图表数据范围
        data = Reference(ws, min_col=3, min_row=start_row, max_row=start_row+len(components)-1)
        cats = Reference(ws, min_col=2, min_row=start_row, max_row=start_row+len(components)-1)
        
        chart.add_data(data)
        chart.set_categories(cats)
        
        # 将图表添加到工作表
        ws.add_chart(chart, "B" + str(start_row + len(components) + 2))
    
    def _create_trend_sheet(self, trend_data):
        """创建健康趋势页"""
        ws = self.wb.create_sheet(title="健康趋势分析")
        
        # 设置列宽
        ws.column_dimensions['A'].width = 5
        ws.column_dimensions['B'].width = 15
        ws.column_dimensions['C'].width = 15
        
        # 标题
        ws['B2'] = "伺服系统健康趋势分析"
        title_font = Font(name='微软雅黑', size=16, bold=True)
        ws['B2'].font = title_font
        ws['B2'].alignment = Alignment(horizontal='center')
        ws.merge_cells('B2:C2')
        
        # 表头
        headers = ["日期", "健康度评分"]
        for i, header in enumerate(headers):
            cell = ws.cell(row=4, column=i+2)
            cell.value = header
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
            cell.fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        
        # 趋势数据
        for i, (date_str, score) in enumerate(trend_data):
            row = i + 5
            ws.cell(row=row, column=2).value = date_str
            ws.cell(row=row, column=3).value = score
        
        # 添加趋势图
        self._add_trend_chart(ws, len(trend_data))
    
    def _add_trend_chart(self, ws, data_length):
        """添加健康趋势图"""
        # 创建折线图
        chart = LineChart()
        chart.title = "系统健康度趋势"
        chart.style = 10
        chart.x_axis.title = "日期"
        chart.y_axis.title = "健康度评分"
        chart.y_axis.scaling.min = 0
        chart.y_axis.scaling.max = 100
        chart.x_axis.number_format = 'yyyy-mm-dd'
        chart.x_axis.majorTimeUnit = "days"
        
        # 设置图表数据范围
        data = Reference(ws, min_col=3, min_row=4, max_row=4+data_length)
        cats = Reference(ws, min_col=2, min_row=5, max_row=4+data_length)
        
        chart.add_data(data, titles_from_data=True)
        chart.set_categories(cats)
        
        # 将图表添加到工作表
        ws.add_chart(chart, "B" + str(4 + data_length + 2))
    
    def _create_rul_sheet(self, assessment_data):
        """创建寿命预测页"""
        ws = self.wb.create_sheet(title="寿命预测分析")
        
        # 设置列宽
        ws.column_dimensions['A'].width = 5
        ws.column_dimensions['B'].width = 20
        ws.column_dimensions['C'].width = 20
        
        # 标题
        ws['B2'] = "伺服系统剩余使用寿命预测"
        title_font = Font(name='微软雅黑', size=16, bold=True)
        ws['B2'].font = title_font
        ws['B2'].alignment = Alignment(horizontal='center')
        ws.merge_cells('B2:C2')
        
        # 预测结果
        ws['B4'] = "预测日期:"
        ws['C4'] = datetime.now().strftime('%Y-%m-%d')
        
        ws['B5'] = "剩余使用寿命:"
        ws['C5'] = f"{assessment_data.get('remaining_hours', 0):.1f}小时"
        
        ws['B6'] = "寿命百分比:"
        ws['C6'] = f"{assessment_data.get('life_percentage', 0):.1f}%"
        
        ws['B7'] = "预测置信度:"
        ws['C7'] = f"{assessment_data.get('confidence', 0):.1f}%"
        
        # 寿命说明
        max_lifetime = 10000  # 设备最大寿命（小时）
        remaining_hours = assessment_data.get('remaining_hours', 0)
        
        ws['B9'] = "寿命说明:"
        explanation = f"根据当前系统运行状态及健康度评估结果，预测系统剩余使用寿命约为{remaining_hours:.1f}小时"
        
        # 根据剩余寿命给出建议
        if remaining_hours < 500:
            explanation += "，剩余寿命已不足5%，建议尽快安排维修或更换设备。"
        elif remaining_hours < 2000:
            explanation += "，剩余寿命已不足20%，建议制定设备更新计划。"
        else:
            explanation += "，设备状态良好，可继续正常使用，建议按照常规维护计划进行定期检查。"
        
        ws['B10'] = explanation
        ws['B10'].alignment = Alignment(wrap_text=True)
        ws.merge_cells('B10:C12')
    
    def generate_report(self, assessment_data, trend_data=None):
        """
        生成健康评估报告
        
        参数:
            assessment_data: 健康评估数据
            trend_data: 健康趋势数据
            
        返回:
            report_bytes: 报告文件的二进制数据
        """
        self._init_workbook()
        
        # 创建报告封面
        self._create_cover_sheet(assessment_data)
        
        # 创建健康详情页
        self._create_health_details_sheet(assessment_data)
        
        # 创建健康趋势页
        if trend_data:
            self._create_trend_sheet(trend_data)
        
        # 创建寿命预测页
        self._create_rul_sheet(assessment_data)
        
        # 将工作簿保存到内存
        output = io.BytesIO()
        self.wb.save(output)
        output.seek(0)
        
        return output.getvalue() 