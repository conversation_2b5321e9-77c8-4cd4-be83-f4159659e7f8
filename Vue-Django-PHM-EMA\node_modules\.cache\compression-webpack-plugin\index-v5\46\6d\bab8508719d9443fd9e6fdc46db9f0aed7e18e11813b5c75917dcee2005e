
3e3e530181704f74b96335272f0d2e676c9168bc	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"name\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"ac4b4dac0a719291c1a92d1e5952ee4e\"}","integrity":"sha512-nSZ3SB0CIfgl33CtVlHLzlNt6pPwYtTOujikZcfGM7xJ8FK/xMELextLEQ09KSfb0OdJT2uQGzPRt6Koy8R8Ig==","time":1753796984113,"size":336747}