{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\home\\index.vue?vue&type=style&index=0&lang=css&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\home\\index.vue", "mtime": 1753423044588}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1634626957199}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1634627893377}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1634627525156}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgojbW9kZWwtY29udGFpbmVyIHsKICBib3JkZXI6c29saWQgNXB4IHJlZDsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwp9Ci5jdGwgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICBsZWZ0OjM5JTsKICB0b3A6MAp9Ci5sYWJlbC1jb2wgewogIHBhZGRpbmc6IDhweCA1cHg7Cn0KI2d1aV9jb250YWluZXJ7CiAgcG9zaXRpb246IGFic29sdXRlOwogIHRvcDogODQlOwogIGxlZnQ6IDgxJTsKfQojZ3VpewovKiDooajnpLpndWkuZG9tRWxlbWVudOayoeaciWhlaWdodOWxnuaApyAqLwogIHRyYW5zZm9ybTp0cmFuc2xhdGUoLTUwJSwgLTc1cHgpOwp9CiNpbmZvQm94IHsKICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgcGFkZGluZzogNXB4OwogIGJhY2tncm91bmQ6ICM3MzczNzQxYTsKICBib3JkZXI6IDNweCBkb3VibGUgd2hpdGVzbW9rZTsKICBib3JkZXItcmFkaXVzOiAxMnB4OwogIGNvbG9yOiB3aGl0ZXNtb2tlOwogIGZvbnQtc2l6ZToxN3B4OwogIG1pbi13aWR0aDogMTYwcHg7CiAgaGVpZ2h0OiA5MHB4OwogIHdpZHRoOiAzODBweAp9CgovKiDkvLrmnI3ns7vnu5/kv6Hmga/pk63niYzmoLflvI8gKi8KI3N5c3RlbUluZm9Cb3ggewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICByaWdodDogMjBweDsKICB0b3A6IDIwcHg7CiAgcGFkZGluZzogMTBweDsKICBiYWNrZ3JvdW5kOiAjNzM3Mzc0MWE7CiAgYm9yZGVyOiAzcHggZG91YmxlIHdoaXRlc21va2U7CiAgYm9yZGVyLXJhZGl1czogMTJweDsKICBjb2xvcjogd2hpdGVzbW9rZTsKICBmb250LXNpemU6IDE0cHg7CiAgbWluLXdpZHRoOiAyMDBweDsKICB3aWR0aDogMjgwcHg7Cn0KCiNzeXN0ZW1JbmZvQm94IGg0IHsKICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgbWFyZ2luLXRvcDogMDsKICBtYXJnaW4tYm90dG9tOiAxMHB4OwogIGZvbnQtc2l6ZTogMTZweDsKICBmb250LXdlaWdodDogYm9sZDsKICBjb2xvcjogI2U2ZTZlNjsKfQoKI3N5c3RlbUluZm9Cb3ggdWwgewogIGxpc3Qtc3R5bGU6IG5vbmU7CiAgcGFkZGluZzogMDsKICBtYXJnaW46IDA7Cn0KCiNzeXN0ZW1JbmZvQm94IGxpIHsKICBtYXJnaW46IDVweCAwOwogIGxpbmUtaGVpZ2h0OiAxLjU7Cn0KCiNzeXN0ZW1JbmZvQm94IHN0cm9uZyB7CiAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogIHdpZHRoOiA4NXB4Owp9CgoKCi8qIOaVhemanOivpuaDheWvueivneahhuagt+W8jyAqLwouZmF1bHQtZGV0YWlscyB7CiAgcGFkZGluZzogMTBweDsKfQoKLmZhdWx0LWRldGFpbHMgaDMgewogIGNvbG9yOiAjRjU2QzZDOwogIG1hcmdpbi10b3A6IDA7CiAgbWFyZ2luLWJvdHRvbTogMTVweDsKICBmb250LXNpemU6IDE4cHg7CiAgdGV4dC1hbGlnbjogY2VudGVyOwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjRUJFRUY1OwogIHBhZGRpbmctYm90dG9tOiAxMHB4Owp9CgouZmF1bHQtZGV0YWlscyBwIHsKICBtYXJnaW46IDEwcHggMDsKICBsaW5lLWhlaWdodDogMS42Owp9CgouZmF1bHQtZGV0YWlscyBzdHJvbmcgewogIGNvbG9yOiAjMzAzMTMzOwogIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICB3aWR0aDogMTAwcHg7CiAgdmVydGljYWwtYWxpZ246IHRvcDsKfQoKLyog6Ieq5a6a5LmJRWwtQWxlcnTmoLflvI8gKi8KLmVsLW1lc3NhZ2UtYm94IHsKICB3aWR0aDogNTAwcHggIWltcG9ydGFudDsKICBtYXgtd2lkdGg6IDkwJTsKfQoKLmVsLW1lc3NhZ2UtYm94X19jb250ZW50IHsKICBtYXgtaGVpZ2h0OiA2MHZoOwogIG92ZXJmbG93LXk6IGF1dG87Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA88BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/home", "sourcesContent": ["<template>\n  <div class=\"home-container\">\n    <div class=\"headtxt\">\n      <h3\n        style=\"font-family: KaiTi;font-size:1.55em;font-weight:bold;\n        margin-top:15px;margin-bottom:5px\"\n      >\n        伺服系统 PHM 软件平台</h3>\n    </div>\n    <div id=\"model-container\">\n      <div class=\"ctl\">\n        <el-button-group>\n          <el-button @click=\"onWindowResize\">刷新3D视图</el-button>\n        </el-button-group>\n      </div>\n      <div v-show=\"infoShow\" id=\"infoBox\">\n        <ul style=\"font-size:20px; line-height:31px\">\n          <li>组件名称: {{ info.name }}</li>\n        </ul>\n      </div>\n      <!-- 伺服系统信息铭牌 -->\n      <div id=\"systemInfoBox\">\n        <h4>伺服系统信息</h4>\n        <ul>\n          <li><strong>型号:</strong> XXXX-EMA</li>\n          <li><strong>额定功率:</strong> 200W</li>\n          <li><strong>额定电压:</strong> 24V DC</li>\n          <li><strong>额定转速:</strong> 3000RPM</li>\n          <li><strong>控制方式:</strong> 闭环位置控制</li>\n          <li><strong>生产日期:</strong> 2023-05-15</li>\n          <li><strong>序列号:</strong> SN20230515001</li>\n        </ul>\n      </div>\n\n\n    </div>\n\n    <!-- 故障提示浮窗组件 -->\n    <fault-notification\n      :visible.sync=\"faultNotificationVisible\"\n      :type=\"faultNotificationType\"\n      :part-name=\"faultNotificationPartName\"\n      :status-name=\"faultNotificationStatusName\"\n      :diagnosis-time=\"faultNotificationTime\"\n      @close=\"handleFaultNotificationClose\"\n      @view-details=\"handleViewDetails\"\n    />\n  </div>\n</template>\n\n<script>\nimport * as THREE from 'three'\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'\nimport { STLLoader } from 'three/examples/jsm/loaders/STLLoader.js'\nimport dat from 'three/examples/js/libs/dat.gui.min.js'\nimport ModelInteractionUtil, {\n  DEFAULT_COLOR,\n  PART_COLOR,\n  GREEN_PARTS\n} from '@/utils/ModelInteractionUtil'\nimport RocketFlameEffect from '@/utils/RocketFlameEffect'\nimport FaultNotification from '@/components/FaultNotification.vue'\nimport {\n  getPartNameByFaultType,\n  getFaultTypeName,\n  getPartChineseName,\n  getStatusType\n} from '@/utils/faultToPartMapping'\n\nexport default {\n  name: '主控台', // eslint-disable-line vue/name-property-casing\n  components: {\n    FaultNotification\n  },\n  data() {\n    return {\n      dataShow: {\n        Xgive: '',\n        Xget: '',\n        Fget: '',\n        healthStatus: ''\n      },\n      dataPermit: false,\n      btntxt: '开始接收数据',\n      // mesh: null,\n      module: null,\n      moduleAll: null,\n      folderName: './3D/',\n      stlName: '',\n      materialCo: DEFAULT_COLOR,\n      camera: null,\n      scene: null,\n      renderer: null,\n      controls: null,\n      infoShow: true,\n      stateShow: true,\n      files: null,\n      info: {\n        name: ''\n      },\n      isFault: false,\n      state: {\n        all: 20,\n        normal: 20,\n        fault: 0\n      },\n      objectStatus: {\n        status: 0\n      },\n      drawFunc: '整体模型',\n      select: '',\n      clickCurrentColor: '',\n      faultCurrentColor: '',\n      p2hDict: null,\n      hanziList: [],\n      selectObject: null,\n      faultObject: null,\n      faultIndex: '',\n      faultObjectName: ['电机_R', '传感器', '控制器'],\n      errorNameDict: {},\n      statusName: '',\n      // 新增模型交互工具实例\n      modelInteraction: null,\n\n      // 火箭尾焰特效相关\n      rocketFlameEffect: null,\n\n      // 故障提示浮窗相关数据\n      faultNotificationVisible: false,\n      faultNotificationType: 'fault', // 'fault' 或 'degradation'\n      faultNotificationPartName: '',\n      faultNotificationStatusName: '',\n      faultNotificationTime: '',\n\n      // 当前故障/退化状态\n      currentFaultType: null,\n      currentFaultPart: null\n    }\n  },\n  mounted() {\n    this.init()\n    const element = document.getElementById('model-container')\n    this.files = require.context('../../../public/3D', false, /.STL$/).keys()\n    this.m = this.$notify.warning({\n      title: '提示',\n      message: '模型正在加载，请稍后进行操作...',\n      duration: 0,\n      showClose: false\n    })\n    // this.onWindowResize()\n    this.loadPre()\n    element.addEventListener('click', this.onMouseClick, false)\n    window.addEventListener('resize', this.onWindowResize, false)\n  },\n  activated() {\n    console.log('进入主控台页面了')\n    // 在页面激活时检查诊断结果\n    this.checkDiagnosisResult()\n  },\n  deactivated() {\n    console.log('离开主控台页面了')\n  },\n  beforeDestroy() {\n    clearInterval(this.timer)\n\n    // 清理火箭尾焰特效\n    if (this.rocketFlameEffect) {\n      this.rocketFlameEffect.destroy()\n      this.rocketFlameEffect = null\n    }\n\n    // 移除事件监听器\n    const element = document.getElementById('model-container')\n    if (element) {\n      element.removeEventListener('click', this.onMouseClick, false)\n    }\n    window.removeEventListener('resize', this.onWindowResize, false)\n  },\n  methods: {\n    // 整体初始化\n    init() {\n      this.createScene()\n      this.helper()\n      // this.initGui()\n      this.createLight()\n      this.createCamera()\n      this.createRender()\n      this.createControls()\n      this.render()\n\n      // 创建一个组来包含所有模型，并设置整体位置\n      this.moduleAll = new THREE.Group()\n      // 将模型放置在网格中央\n      this.moduleAll.position.set(0, 0, 0)\n      this.scene.add(this.moduleAll)\n\n      // 初始化模型交互工具，添加状态变化回调\n      this.modelInteraction = new ModelInteractionUtil(\n        this.scene,\n        this.info,\n        this.handleModelStatusChange\n      )\n\n      // 应用新的颜色设置\n      this.modelInteraction.resetAllModelsColor()\n\n      // 检查Vuex中是否有未处理的诊断结果\n      this.checkDiagnosisResult()\n    },\n\n    // 初始化火箭尾焰特效\n    initRocketFlameEffect() {\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (daodanModel) {\n        try {\n          this.rocketFlameEffect = new RocketFlameEffect(this.scene, daodanModel, {\n            intensity: 1,    // 固定强度值\n            speed: 3,        // 固定速度值\n            turbulence: 1    // 固定湍流值\n          })\n          console.log('火箭尾焰特效已初始化')\n        } catch (error) {\n          console.error('初始化火箭尾焰特效失败:', error)\n        }\n      }\n    },\n\n\n\n    // 检查Vuex中是否有未处理的诊断结果\n    checkDiagnosisResult() {\n      console.log('检查诊断结果', this.$store.getters['diagnosis/hasUnprocessedResult'])\n      if (this.$store.getters['diagnosis/hasUnprocessedResult']) {\n        const result = this.$store.getters['diagnosis/currentDiagnosisResult']\n        console.log('发现未处理的诊断结果:', result)\n        this.handleDiagnosisResult(result)\n        this.$store.dispatch('diagnosis/processDiagnosisResult')\n        console.log('诊断结果处理完成')\n      } else {\n        console.log('没有未处理的诊断结果')\n      }\n    },\n\n    // 处理模型状态变化\n    handleModelStatusChange(statusData) {\n      console.log('模型状态变化:', statusData)\n\n      if (statusData.type === 'fault' || statusData.type === 'degradation') {\n        // 更新状态计数\n        this.state.fault = 1\n        this.state.normal = this.state.all - this.state.fault\n        this.isFault = statusData.type === 'fault'\n\n        // 获取部件中文名称\n        const partChineseName = getPartChineseName(statusData.object.name)\n        console.log('部件中文名称:', partChineseName)\n\n        // 更新状态名称\n        this.statusName = statusData.info.statusName\n        console.log('状态名称:', this.statusName)\n\n        // 显示故障提示浮窗\n        this.showFaultNotification({\n          type: statusData.type,\n          partName: partChineseName,\n          statusName: statusData.info.statusName,\n          diagnosisTime: statusData.info.diagnosisTime\n        })\n\n        console.log('已显示故障提示浮窗')\n      } else {\n        // 重置状态\n        this.state.fault = 0\n        this.state.normal = this.state.all\n        this.isFault = false\n        this.statusName = ''\n        console.log('已重置状态')\n      }\n    },\n\n    // 显示故障提示浮窗\n    showFaultNotification(notificationData) {\n      console.log('显示故障提示浮窗:', notificationData)\n      this.faultNotificationType = notificationData.type\n      this.faultNotificationPartName = notificationData.partName\n      this.faultNotificationStatusName = notificationData.statusName\n      this.faultNotificationTime = notificationData.diagnosisTime\n      this.faultNotificationVisible = true\n\n      // 确保浮窗在DOM更新后显示\n      this.$nextTick(() => {\n        console.log('浮窗显示状态:', this.faultNotificationVisible)\n      })\n    },\n\n    // 处理故障提示浮窗关闭\n    handleFaultNotificationClose() {\n      this.faultNotificationVisible = false\n    },\n\n    // 处理故障诊断结果\n    handleDiagnosisResult(result) {\n      console.log('处理诊断结果:', result)\n\n      // 如果诊断成功\n      if (result && result.success) {\n        const diagnosisDetails = result.diagnosis_details\n        const conclusion = diagnosisDetails.conclusion\n        const faultType = conclusion.predicted_fault_mode\n\n        console.log('诊断结论:', faultType)\n\n        // 如果是正常状态，重置所有部件状态\n        if (faultType === '0_normal') {\n          console.log('诊断结果为正常状态，重置所有部件')\n          this.resetAllPartsStatus()\n          return\n        }\n\n        // 获取对应的部件名称\n        const partName = getPartNameByFaultType(faultType)\n        if (!partName) {\n          console.warn('未找到对应的部件:', faultType)\n          return\n        }\n\n        console.log('对应的部件名称:', partName)\n\n        // 获取状态类型和状态名称\n        const statusType = getStatusType(faultType)\n        const statusName = getFaultTypeName(faultType)\n\n        console.log('状态类型:', statusType, '状态名称:', statusName)\n\n        // 获取当前时间作为诊断时间\n        const diagnosisTime = new Date().toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit',\n          second: '2-digit',\n          hour12: false\n        })\n\n        // 保存当前故障信息\n        this.currentFaultType = faultType\n        this.currentFaultPart = partName\n\n        // 根据状态类型标记部件\n        if (statusType === 'fault') {\n          // 故障状态，标记为红色\n          console.log('标记部件为故障状态:', partName)\n          this.modelInteraction.markAsFault(partName, {\n            statusName,\n            diagnosisTime\n          })\n        } else if (statusType === 'degradation') {\n          // 退化状态，标记为褐色\n          console.log('标记部件为退化状态:', partName)\n          this.modelInteraction.markAsDegradation(partName, {\n            statusName,\n            diagnosisTime\n          })\n        }\n      } else {\n        // 诊断失败\n        console.error('诊断失败:', result ? result.message : '未知错误')\n      }\n    },\n\n    // 重置所有部件状态\n    resetAllPartsStatus() {\n      if (this.currentFaultPart) {\n        this.modelInteraction.resetStatus(this.currentFaultPart)\n        this.currentFaultPart = null\n        this.currentFaultType = null\n\n        // 重置状态计数\n        this.state.fault = 0\n        this.state.normal = this.state.all\n        this.isFault = false\n        this.statusName = ''\n      }\n    },\n\n    // 测试故障诊断\n    testFaultDiagnosis() {\n      console.log('测试故障诊断')\n\n      // 创建一个模拟的诊断结果\n      const mockDiagnosisResult = {\n        success: true,\n        diagnosis_details: {\n          conclusion: {\n            predicted_fault_mode: '4_fault_stator_short'\n          }\n        }\n      }\n\n      // 处理诊断结果\n      this.handleDiagnosisResult(mockDiagnosisResult)\n    },\n\n    // 三大件 创建场景\n    createScene() {\n      this.scene = new THREE.Scene()\n    },\n\n    // 创建相机\n    createCamera() {\n      const element = document.getElementById('model-container')\n      const k = element.clientWidth / element.clientHeight // 实际3d显示窗口宽高比\n      this.camera = new THREE.PerspectiveCamera(45, k, 0.1, 10000) // 透视相机 (for,aspect,near,far) 视场 长宽比 多近开始渲染 最远看到的距离\n      // 调整相机位置，以便更好地观察模型\n      this.camera.position.set(0, 400, 600)\n      this.camera.lookAt(new THREE.Vector3(0, 0, 0))\n      this.scene.add(this.camera)\n    },\n\n    // 创建渲染器\n    createRender() {\n      const element = document.getElementById('model-container')\n      this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })\n      this.renderer.setSize(element.clientWidth, element.clientHeight) // 设置渲染区域尺寸\n      this.renderer.shadowMap.enabled = true\n      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap\n      this.renderer.setClearColor(0x050505, 0.6)\n      element.appendChild(this.renderer.domElement) // 渲染div到canvas\n    },\n\n    // 渲染\n    render() {\n      // 确保导弹壳体始终保持透明\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (daodanModel && daodanModel.material && this.modelInteraction) {\n        this.modelInteraction.updateDaodanMaterial(daodanModel)\n      }\n\n      // 更新火箭尾焰特效\n      if (this.rocketFlameEffect) {\n        this.rocketFlameEffect.update()\n      }\n\n      this.renderer.render(this.scene, this.camera)\n      requestAnimationFrame(this.render)\n    },\n\n    // 底部网格和三轴指示器\n    helper() {\n      var gridplane = new THREE.GridHelper(1200, 60, 0xFF7F50, 0x4A4A4A) // 一格20\n      this.scene.add(gridplane)\n      const axes = new THREE.AxesHelper(200)\n      this.scene.add(axes)\n    },\n\n    // 控制模型组件\n    initGui() {\n      this.paras = {\n        // rotationSpeed: 0.005 // 中文也行\n        rotationSpeed: 0 // 中文也行\n      }\n      var gui = new dat.GUI({ autoPlace: false })\n      gui.domElement.id = 'gui'\n      document.getElementById('gui_container').appendChild(gui.domElement)\n      gui.add(this.paras, 'rotationSpeed', 0, 0.05)\n    },\n\n    // 创建光源\n    createLight() {\n      // 环境光 没有特定的光源，该光源不会影响阴影的产生，被应用到全局范围内的所有对象,使用该光源是为了弱化阴影或者添加一些颜色\n      const ambientLight = new THREE.AmbientLight(0x999999)\n      this.scene.add(ambientLight)\n      const point = new THREE.PointLight(0xffffff)\n      // const point = new THREE.SpotLight(0xffffff)\n      point.position.set(-300, 600, -400)\n      this.scene.add(point)\n    },\n\n    // 轨道控制、旋转缩放\n    createControls() {\n      this.controls = new OrbitControls(this.camera, this.renderer.domElement)\n    },\n\n    // 接收数据控制\n    dataGet() {\n      if (this.btntxt === '停止接收数据') {\n        this.btntxt = '开始接收数据'\n        this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n        if (this.faultCurrentColor === 14423100) {\n          this.faultObject.material.color.set(0x7CFC00)\n        } else {\n          this.faultObject.material.color.set(this.faultCurrentColor)\n        }\n      } else {\n        this.btntxt = '停止接收数据'\n        // 目前认为故障组件只有EMA总装一种，提前指定故障组件 应该在getData()中按照faultIndex再确定\n        this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n        this.faultCurrentColor = this.faultObject.material.color.getHex()\n        console.log('inital faultObjectColor', this.faultCurrentColor)\n        this.$axios.get('./errorDict.json').then(res => {\n          this.errorNameDict = res.data.errorNameDict\n          // console.log('字典', res.data.errorNameDict)\n        })\n      }\n      this.dataPermit = !this.dataPermit\n      console.log(this.dataPermit)\n      this.getData()\n    },\n\n    // 接收数据\n    getData() {\n      if (this.dataPermit) {\n        /* this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n        this.faultCurrentColor = this.faultObject.material.color.getHex() */\n        this.timer = setInterval(() => {\n          this.$axios.get('/phm/getData/').then((res) => {\n            // console.log('111222', res.data.data, typeof (res.data.data)) 类型是object\n            this.dataShow.Xget = res.data.data.x_get\n            this.dataShow.Xgive = res.data.data.x_give\n            this.dataShow.Fget = res.data.data.f_get\n            // cw, ch, r, k, x, y, z\n            this.dataPanel(1600, 840, 50, 6, -380, 0, 210)\n            this.faultIndex = res.data.data.faultStatus\n            this.dataShow.healthStatus = res.data.data.healthStatus\n            this.statusName = this.errorNameDict[this.faultIndex]\n            console.log('faultIndex', this.faultIndex)\n            if (this.faultIndex !== '0') {\n              this.isFault = true\n              this.objectStatus.status = 1\n              this.state.fault = 1\n              this.state.normal = 19\n              // if (this.faultIndex !== '10') {\n              //   this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n              // }\n              this.faultObject.material.color.set(0xDC143C)\n            } else {\n              this.isFault = false\n              this.objectStatus.status = 0\n              this.faultObject.material.color.set(this.faultCurrentColor)\n              this.state.fault = 0\n              this.state.normal = 20\n            }\n          })\n        }, this.$Common.requestInterval)\n      } else {\n        clearInterval(this.timer)\n      }\n    },\n\n    // 加载STL模型\n    loadSTL(stlName) {\n      console.log('加载STL模型:', stlName)\n      // const THIS = this // eslint-disable-line no-unused-vars\n      const loader = new STLLoader()\n      loader.load(\n        this.folderName + stlName,\n        geometry => {\n          // 确保几何体居中\n          geometry.computeBoundingBox()\n\n          // 为导弹壳体模型设置特殊材质\n          let material\n          if (stlName === 'daodan.STL') {\n            material = new THREE.MeshPhongMaterial({\n              color: 0xffffff, // 白色\n              transparent: true,\n              opacity: 0.10, // 更高的透明度\n              side: THREE.DoubleSide, // 双面渲染\n              depthWrite: false, // 禁用深度写入以避免渲染问题\n              depthTest: false, // 禁用深度测试，使射线能够穿透\n              shininess: 150 // 增加光泽度\n            })\n          } else {\n            // 检查是否为需要设置为绿色的部件\n            const partName = stlName.split('.')[0] // 去掉.STL后缀\n            let color = DEFAULT_COLOR // 默认蓝色\n\n            // 检查是否为需要设置为绿色的部件\n            for (const greenPart of GREEN_PARTS) {\n              if (partName.includes(greenPart)) {\n                color = PART_COLOR // 设置为绿色\n                break\n              }\n            }\n\n            material = new THREE.MeshStandardMaterial({\n              color: color, // 使用确定的颜色\n              transparent: true,\n              opacity: 0.7\n            })\n          }\n\n          this.module = new THREE.Mesh(geometry, material) // 创建网格对象\n          this.module.name = stlName.split('.')[0] // 去掉.STL后缀\n          console.log('创建模型部件:', this.module.name)\n          this.info.name = '模型正在加载...'\n\n          // 旋转模型使底座底面与网格平行\n          this.module.rotateX(-Math.PI / 2) // 绕X轴旋转，使模型水平放置\n          this.module.rotateX(Math.PI / 2) // 绕X轴顺时针旋转90度\n\n          // 将所有模型添加到模型组\n          this.moduleAll.add(this.module)\n\n          // 如果是导弹模型，需要特殊处理\n          if (stlName === 'daodan.STL') {\n            console.log('处理导弹壳体模型位置')\n\n            // 设置导弹壳体的特殊属性，使其不阻挡交互\n            this.module.renderOrder = -1 // 负数renderOrder确保它在其他对象之前渲染\n            this.module.userData.isBackground = true // 标记为背景对象\n\n            // 计算所有模型的包围盒\n            const box = new THREE.Box3().setFromObject(this.moduleAll)\n            const center = box.getCenter(new THREE.Vector3())\n            const size = box.getSize(new THREE.Vector3())\n\n            // 重置导弹模型的位置和旋转\n            this.module.position.set(0, 0, 0)\n            this.module.rotation.set(0, 0, 0)\n\n            // 首先水平放置导弹模型\n            this.module.rotateX(-Math.PI / 2)\n\n            // 然后逆时针旋转90度，使导弹壳体与其他零件平行\n            this.module.rotateZ(Math.PI / 2)\n\n            // 计算导弹模型的包围盒\n            const daodanBox = new THREE.Box3().setFromObject(this.module)\n            const daodanSize = daodanBox.getSize(new THREE.Vector3())\n\n            // 调整导弹模型的缩放，确保能包含所有模型\n            // 增加缩放系数，使导弹壳体更大，伺服系统可以完全放在内部\n            const scaleFactor = Math.max(\n              size.x / daodanSize.x,\n              size.y / daodanSize.y,\n              size.z / daodanSize.z\n            ) * 2.5 // 放大3.5倍，确保伺服系统完全位于内部\n\n            this.module.scale.set(scaleFactor, scaleFactor, scaleFactor)\n\n            // 将导弹模型放置在所有模型的中心\n            this.module.position.copy(center)\n\n            // 计算导弹壳体的尺寸\n            const scaledDaodanLength = daodanSize.z * scaleFactor\n            const scaledDaodanWidth = daodanSize.x * scaleFactor\n            const scaledDaodanHeight = daodanSize.y * scaleFactor\n\n            // 根据图片反馈调整位置\n            // 向下移动导弹壳体（Y轴负方向）\n            this.module.position.y -= size.y * 1.3 // 向下移动\n\n            // 向右移动导弹壳体（Z轴正方向，由于旋转了90度）\n            this.module.position.z += size.z * 3.7 // 向右移动\n\n            // 向后移动导弹壳体（X轴正方向，由于旋转了90度）\n            this.module.position.x += scaledDaodanLength * 0.6// 向后移动\n\n            console.log('导弹壳体模型已调整位置和大小', {\n              center,\n              size,\n              scaleFactor,\n              daodanSize,\n              scaledDaodanLength,\n              scaledDaodanWidth,\n              scaledDaodanHeight,\n              adjustedPosition: this.module.position\n            })\n\n            // 延迟初始化火箭尾焰特效，确保导弹模型完全加载和定位\n            setTimeout(() => {\n              this.initRocketFlameEffect()\n            }, 800)\n          }\n\n          // 如果加载完成底座，说明主要模型都已加载完成\n          if (stlName === '底座.STL') {\n            // 调整整个模型组的位置，使其居中\n            this.moduleAll.position.set(0, 0, 0)\n\n            // 计算整个模型组的包围盒，用于居中\n            const box = new THREE.Box3().setFromObject(this.moduleAll)\n            const center = box.getCenter(new THREE.Vector3())\n\n            // 将模型移动到网格中央\n            this.moduleAll.position.x = -center.x\n            this.moduleAll.position.z = -center.z\n            // 保持y轴位置，使底座底面与网格平行\n            this.moduleAll.position.y = -box.min.y + 10 // 稍微抬高一点，避免穿过网格\n\n            this.m.close()\n            this.$notify.success({\n              title: '提示',\n              message: '模型加载完毕'\n            })\n            this.info.name = '待单击模型...'\n            console.log('模型已加载并居中放置')\n\n            // 打印所有模型部件名称，用于调试\n            console.log('所有模型部件:')\n            this.scene.traverse((object) => {\n              if (object.isMesh) {\n                console.log(' - ' + object.name)\n              }\n            })\n\n            // 确保所有部件都应用了正确的颜色\n            if (this.modelInteraction) {\n              console.log('应用颜色设置...')\n              this.modelInteraction.resetAllModelsColor()\n            }\n          }\n        }\n      )\n    },\n\n    // 改变显示组件重新绘制\n    loadPre() { // 加载哪个零件\n      // 先加载除导弹外的所有主要部件\n      let daodanModel = null\n      const otherModels = []\n\n      // 遍历所有模型文件\n      for (var i = 0; i < this.files.length; i++) {\n        var stlName = this.files[i].split('/')[1]\n        // 分开处理导弹模型和其他模型\n        if (stlName === 'daodan.STL') {\n          // 先记录导弹模型，稍后加载\n          daodanModel = stlName\n        } else if (!stlName.includes('GB╱T') && !stlName.includes('螺钉')) {\n          // 只加载主要零件，减少加载时间\n          this.loadSTL(stlName)\n          otherModels.push(stlName)\n        }\n      }\n\n      // 在所有其他模型加载完成后，最后加载导弹壳体模型\n      if (daodanModel) {\n        setTimeout(() => {\n          console.log('加载导弹壳体模型，包含所有其他模型')\n          this.loadSTL(daodanModel)\n        }, 1000) // 延迟加载，确保其他模型已经加载完成\n      }\n    },\n\n    // 调整导弹模型位置\n    adjustDaodanPosition() {\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (!daodanModel) return\n\n      // 计算所有其他模型的包围盒\n      const otherModelsBox = new THREE.Box3()\n      this.scene.traverse((object) => {\n        if (object.isMesh && object.name !== 'daodan') {\n          otherModelsBox.expandByObject(object)\n        }\n      })\n\n      // 计算导弹模型的包围盒\n      const daodanBox = new THREE.Box3().setFromObject(daodanModel)\n\n      // 调整导弹模型的位置，使其包含所有其他模型\n      // 并将其他模型放在导弹模型的尾部\n      const otherModelsCenter = otherModelsBox.getCenter(new THREE.Vector3())\n      const daodanCenter = daodanBox.getCenter(new THREE.Vector3())\n\n      // 计算需要移动的距离\n      const offsetX = otherModelsCenter.x - daodanCenter.x\n      const offsetY = otherModelsCenter.y - daodanCenter.y\n      const offsetZ = otherModelsCenter.z - daodanCenter.z\n\n      // 移动导弹模型\n      daodanModel.position.set(\n        offsetX,\n        offsetY,\n        offsetZ\n      )\n\n      console.log('调整了导弹模型位置')\n    },\n\n    // 数据面板\n    dataPanel(cw, ch, r, k, x, y, z) {\n      // 用canvas生成图片\n      var color = ['#008000', '#FF0000']\n      var canvas = document.createElement('canvas')\n      const ctx = canvas.getContext('2d')\n      canvas.width = cw\n      canvas.height = ch\n      ctx.lineWidth = 10\n      ctx.fillStyle = 'rgba(255,255,255,1)'\n      this.roundRect(ctx, 0, 0, cw, ch, r)\n      var gradient = ctx.createLinearGradient(0, 0, canvas.width, 0)\n      gradient.addColorStop('0', 'blue')\n      gradient.addColorStop('1.0', 'red')\n      ctx.font = 'normal 80pt \"楷体\"'\n      ctx.fillStyle = color[this.objectStatus.status]\n      ctx.fillText(this.statusName, 700, 150)\n      ctx.fillStyle = color[0]\n      // ctx.fillText('0.83', 700, 300)\n      ctx.fillText(this.dataShow.healthStatus, 700, 300)\n      ctx.fillStyle = gradient\n      ctx.fillText('当前状态：', 60, 150)\n      ctx.fillText('健康值：', 60, 300)\n      ctx.fillText('位移指令：', 60, 450)\n      ctx.fillText('实际位移：', 60, 600)\n      ctx.fillText('负载力：', 60, 750)\n      ctx.fillText(this.dataShow.Xgive, 700, 450)\n      ctx.fillText(this.dataShow.Xget, 700, 600)\n      ctx.fillText(this.dataShow.Fget, 700, 750)\n      // canvas.height = 500\n      const url = canvas.toDataURL('./img/png')\n      var geometry = new THREE.PlaneGeometry(cw / k, ch / k)\n      var texture = new THREE.TextureLoader().load(url)\n      // 将图像加载为纹理，然后将纹理赋给材质的map属性\n      var material = new THREE.MeshBasicMaterial({\n        map: texture,\n        side: THREE.DoubleSide,\n        opacity: 1,\n        transparent: true\n      })\n      const rect = new THREE.Mesh(geometry, material)\n      rect.position.set(x, z, y)\n      this.scene.add(rect)\n    },\n\n    // 画圆角矩形\n    roundRect(ctx, x, y, w, h, r) {\n      ctx.beginPath()\n      ctx.moveTo(x + r, y)\n      ctx.arcTo(x + w, y, x + w, y + h, r)\n      ctx.arcTo(x + w, y + h, x, y + h, r)\n      ctx.arcTo(x, y + h, x, y, r)\n      ctx.arcTo(x, y, x + w, y, r)\n      ctx.fill()\n      ctx.closePath()\n    },\n\n    // 监听函数部分 - 使用新的模型交互工具\n    onMouseClick(event) {\n      // 加载字典数据\n      this.$axios.get('./errorDict.json').then(res => {\n        this.p2hDict = res.data.pinyin2hanzi\n        const obj = this.p2hDict\n        this.hanziList = Object.keys(obj)\n      })\n\n      // 确保导弹壳体始终保持透明\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (daodanModel && daodanModel.material) {\n        this.modelInteraction.updateDaodanMaterial(daodanModel)\n      }\n\n      const element = document.getElementById('model-container')\n      var raycaster = new THREE.Raycaster()\n      var mouse = new THREE.Vector2()\n\n      // 将鼠标点击位置的屏幕坐标转成threejs中的标准坐标\n      mouse.x = (event.offsetX / element.clientWidth) * 2 - 1\n      mouse.y = -(event.offsetY / element.clientHeight) * 2 + 1\n\n      // 通过摄像机和鼠标位置更新射线\n      raycaster.setFromCamera(mouse, this.camera)\n\n      // 计算物体和射线的焦点\n      var intersects = raycaster.intersectObjects(this.scene.children, true)\n        .filter(intersect => {\n          // 过滤掉导弹壳体，使其不响应点击\n          return intersect.object.name !== 'daodan' &&\n                 !intersect.object.userData.isBackground\n        })\n\n      if (intersects.length > 0) {\n        // 处理点击到的对象\n        this.modelInteraction.handleModelClick(intersects[0].object)\n      } else {\n        // 点击到空白区域\n        this.modelInteraction.handleBackgroundClick()\n      }\n    },\n\n    // 检测区域大小变化\n    onWindowResize() {\n      const element = document.getElementById('model-container')\n      this.camera.aspect = element.clientWidth / element.clientHeight\n      this.camera.updateProjectionMatrix()\n      this.render()\n      // 设置渲染区域尺寸\n      this.renderer.setSize(element.clientWidth, element.clientHeight)\n      console.log('3d area changes')\n\n      // 恢复到初始正常状态\n      this.resetAllPartsStatus()\n\n      // 清除Vuex中的诊断结果\n      this.$store.dispatch('diagnosis/clearDiagnosisResult')\n\n      // 重置信息显示\n      this.info.name = '待单击模型...'\n\n      console.log('已恢复到初始正常状态')\n    },\n\n    // 处理view-details事件\n    handleViewDetails() {\n      console.log('查看详情:', this.currentFaultType, this.currentFaultPart)\n\n      // 如果当前有故障或退化状态\n      if (this.currentFaultType && this.currentFaultPart) {\n        // 显示详细信息对话框\n        this.$alert(`\n          <div class=\"fault-details\">\n            <h3>${this.faultNotificationStatusName}</h3>\n            <p><strong>部件名称:</strong> ${this.faultNotificationPartName}</p>\n            <p><strong>故障类型:</strong> ${this.currentFaultType}</p>\n            <p><strong>诊断时间:</strong> ${this.faultNotificationTime}</p>\n            <p><strong>可能原因:</strong> ${this.getPossibleCauses(this.currentFaultType)}</p>\n            <p><strong>建议解决方案:</strong> ${this.getSuggestedSolutions(this.currentFaultType)}</p>\n          </div>\n        `, '故障详情', {\n          dangerouslyUseHTMLString: true,\n          confirmButtonText: '确定',\n          callback: action => {\n            console.log(action)\n          }\n        })\n      }\n    },\n\n    // 获取可能原因\n    getPossibleCauses(faultType) {\n      const causes = {\n        '1_degradation_magnet': '长时间使用导致永磁体性能下降；环境温度过高；机械冲击或振动。',\n        '2_degradation_brush_wear': '正常磨损；过载运行；环境中存在过多粉尘。',\n        '3_degradation_commutator_oxidation': '环境湿度过高；长期不使用；电刷与换向器接触不良。',\n        '4_fault_stator_short': '绝缘材料老化；过载运行；绕组温度过高；制造缺陷。',\n        '5_fault_rotor_open': '机械损伤；过载运行；焊接点断裂；制造缺陷。',\n        '6_degradation_bearing_wear': '正常磨损；润滑不足；轴承负载过大；轴承安装不当。',\n        '7_fault_bearing_stuck': '润滑失效；异物进入；轴承严重磨损；轴承锈蚀。',\n        '8_degradation_gear_wear': '正常磨损；润滑不足；齿轮负载过大；齿轮材料缺陷。',\n        '9_degradation_sensor_drift': '长期使用导致性能下降；环境温度变化；电源电压波动。',\n        '10_fault_sensor_loss': '传感器连接松动；传感器损坏；信号线断路；电源故障。',\n        '11_fault_mosfet_breakdown': '过电压；过电流；温度过高；静电放电损伤。',\n        '12_degradation_drive_distortion': '电路元件老化；电源电压不稳；信号干扰；温度变化。',\n        '13_fault_mcu_crash': '软件错误；电源问题；硬件故障；外部干扰。'\n      }\n      return causes[faultType] || '未知原因'\n    },\n\n    // 获取建议解决方案\n    getSuggestedSolutions(faultType) {\n      const solutions = {\n        '1_degradation_magnet': '更换永磁体；降低工作环境温度；减少机械冲击。',\n        '2_degradation_brush_wear': '更换电刷；检查负载是否过大；清洁工作环境。',\n        '3_degradation_commutator_oxidation': '清洁换向器表面；降低环境湿度；定期维护。',\n        '4_fault_stator_short': '更换定子绕组；检查负载情况；改善散热条件。',\n        '5_fault_rotor_open': '更换转子绕组；检查焊接点；降低负载。',\n        '6_degradation_bearing_wear': '更换轴承；增加润滑；检查轴承安装情况。',\n        '7_fault_bearing_stuck': '更换轴承；清洁轴承；检查润滑情况。',\n        '8_degradation_gear_wear': '更换齿轮；增加润滑；检查负载情况。',\n        '9_degradation_sensor_drift': '校准传感器；更换传感器；稳定工作环境。',\n        '10_fault_sensor_loss': '检查连接；更换传感器；检查信号线路。',\n        '11_fault_mosfet_breakdown': '更换MOSFET；检查电路保护措施；改善散热条件。',\n        '12_degradation_drive_distortion': '更换老化元件；稳定电源电压；增加信号滤波。',\n        '13_fault_mcu_crash': '更新软件；检查电源；更换MCU；增加抗干扰措施。'\n      }\n      return solutions[faultType] || '请联系专业维修人员'\n    }\n  }\n}\n</script>\n<style>\n#model-container {\n  border:solid 5px red;\n  position: absolute;\n  width: 100%;\n  height: 100%;\n}\n.ctl {\n  position: absolute;\n  left:39%;\n  top:0\n}\n.label-col {\n  padding: 8px 5px;\n}\n#gui_container{\n  position: absolute;\n  top: 84%;\n  left: 81%;\n}\n#gui{\n/* 表示gui.domElement没有height属性 */\n  transform:translate(-50%, -75px);\n}\n#infoBox {\n  position: absolute;\n  padding: 5px;\n  background: #7373741a;\n  border: 3px double whitesmoke;\n  border-radius: 12px;\n  color: whitesmoke;\n  font-size:17px;\n  min-width: 160px;\n  height: 90px;\n  width: 380px\n}\n\n/* 伺服系统信息铭牌样式 */\n#systemInfoBox {\n  position: absolute;\n  right: 20px;\n  top: 20px;\n  padding: 10px;\n  background: #7373741a;\n  border: 3px double whitesmoke;\n  border-radius: 12px;\n  color: whitesmoke;\n  font-size: 14px;\n  min-width: 200px;\n  width: 280px;\n}\n\n#systemInfoBox h4 {\n  text-align: center;\n  margin-top: 0;\n  margin-bottom: 10px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #e6e6e6;\n}\n\n#systemInfoBox ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n#systemInfoBox li {\n  margin: 5px 0;\n  line-height: 1.5;\n}\n\n#systemInfoBox strong {\n  display: inline-block;\n  width: 85px;\n}\n\n\n\n/* 故障详情对话框样式 */\n.fault-details {\n  padding: 10px;\n}\n\n.fault-details h3 {\n  color: #F56C6C;\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 18px;\n  text-align: center;\n  border-bottom: 1px solid #EBEEF5;\n  padding-bottom: 10px;\n}\n\n.fault-details p {\n  margin: 10px 0;\n  line-height: 1.6;\n}\n\n.fault-details strong {\n  color: #303133;\n  display: inline-block;\n  width: 100px;\n  vertical-align: top;\n}\n\n/* 自定义El-Alert样式 */\n.el-message-box {\n  width: 500px !important;\n  max-width: 90%;\n}\n\n.el-message-box__content {\n  max-height: 60vh;\n  overflow-y: auto;\n}\n</style>\n\n"]}]}