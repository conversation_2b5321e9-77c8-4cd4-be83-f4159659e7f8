# Generated by Django 3.1.7 on 2021-06-16 07:37

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=200, verbose_name='名称')),
                ('icon', models.Char<PERSON><PERSON>(max_length=200, verbose_name='头像')),
                ('email', models.EmailField(max_length=254, null=True, verbose_name='邮箱')),
                ('pass_word', models.CharField(max_length=200, verbose_name='密码')),
                ('qq', models.CharField(max_length=20, verbose_name='QQ')),
                ('phone', models.CharField(max_length=20, verbose_name='手机号')),
                ('wx', models.Cha<PERSON><PERSON><PERSON>(max_length=200, verbose_name='微信号')),
                ('auth', models.IntegerField(choices=[(0, '普通用户'), (1, '管理员')], verbose_name='权限')),
                ('gender', models.IntegerField(choices=[(0, '男'), (1, '女')], null=True, verbose_name='性别')),
                ('status', models.IntegerField(choices=[(0, '未毕业'), (1, '已毕业')], null=True, verbose_name='毕业状态')),
                ('student_number', models.CharField(max_length=20, verbose_name='学号')),
                ('admission_time', models.DateTimeField(null=True, verbose_name='入学时间')),
                ('graduation_time', models.DateTimeField(null=True, verbose_name='毕业时间')),
                ('graduation_destination', models.CharField(max_length=200, verbose_name='毕业去向')),
                ('info', models.CharField(max_length=5000, verbose_name='个人简介')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
            },
        ),
    ]
