
5cf3bf420c2c57465674912eb884adf05cf5b3ed	{"key":"{\"nodeVersion\":\"v16.20.2\",\"compression-webpack-plugin\":\"6.1.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"brotliCompress\",\"compressionOptions\":{\"params\":{\"1\":11}},\"name\":\"static\\u002Fjs\\u002Fruntime.js\",\"contentHash\":\"dd9c24aa6487e54ccf50ebb6dca9a1e3\"}","integrity":"sha512-ZuXvivRpdjV3X1FTkhdseatTcD7A79Zi6kQXjyMiHLtua41OfjILtQxGIn91YsWO2B/XoGHK+vEa8a21HpTu0Q==","time":1753796984478,"size":23574}