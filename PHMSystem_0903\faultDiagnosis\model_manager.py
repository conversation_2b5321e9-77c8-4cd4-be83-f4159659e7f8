import os
import json
import tensorflow as tf
from django.conf import settings
import shutil
import time

class ModelManager:
    """
    诊断模型管理类，负责模型的加载、列表获取和动态更新
    """
    def __init__(self, models_dir):
        """
        初始化模型管理器
        :param models_dir: 模型存储目录
        """
        self.models_dir = models_dir
        self.models_cache = {}  # 缓存已加载的模型
        self.models_info = {}   # 存储模型的元信息
        
        # 确保模型目录存在
        if not os.path.exists(self.models_dir):
            os.makedirs(self.models_dir)
            
        # 初始化时加载模型列表
        self.refresh_models_list()
    
    def refresh_models_list(self):
        """
        刷新模型列表
        :return: 模型文件列表
        """
        model_files = []
        
        # 获取所有.h5文件
        for file in os.listdir(self.models_dir):
            if file.endswith('.h5'):
                model_path = os.path.join(self.models_dir, file)
                model_files.append(file)
                
                # 更新模型信息
                if file not in self.models_info:
                    # 获取文件的创建时间和大小
                    stats = os.stat(model_path)
                    creation_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stats.st_mtime))
                    size_mb = round(stats.st_size / (1024 * 1024), 2)
                    
                    self.models_info[file] = {
                        'name': file,
                        'path': model_path,
                        'creation_time': creation_time,
                        'size_mb': size_mb
                    }
        
        return model_files
    
    def get_models_list(self):
        """
        获取模型列表及其详细信息
        :return: 模型信息列表
        """
        self.refresh_models_list()
        return list(self.models_info.values())
    
    def load_model(self, model_name):
        """
        加载指定的模型
        :param model_name: 模型文件名
        :return: 加载的模型对象，如果加载失败则返回None
        """
        model_path = os.path.join(self.models_dir, model_name)
        
        # 检查模型文件是否存在
        if not os.path.exists(model_path):
            return None
        
        # 如果模型已经加载过，直接从缓存返回
        if model_name in self.models_cache:
            return self.models_cache[model_name]
        
        # 加载模型
        try:
            # 使用TensorFlow的线程安全方式加载模型
            with tf.device('/cpu:0'):  # 强制在CPU上加载，避免GPU内存问题
                model = tf.keras.models.load_model(model_path, compile=False)
                self.models_cache[model_name] = model
                return model
        except Exception as e:
            print(f"Error loading model {model_name}: {e}")
            return None
    
    def add_model(self, source_path, new_name=None):
        """
        添加新模型到模型目录
        :param source_path: 源模型文件路径
        :param new_name: 新的模型名称，如果为None则使用原文件名
        :return: 是否添加成功
        """
        try:
            # 检查源文件是否存在
            if not os.path.exists(source_path):
                return False, f"源文件不存在: {source_path}"
            
            # 如果未指定新名称，使用原文件名
            if new_name is None:
                new_name = os.path.basename(source_path)
            
            # 确保文件名以.h5结尾
            if not new_name.endswith('.h5'):
                new_name += '.h5'
            
            # 目标路径
            target_path = os.path.join(self.models_dir, new_name)
            
            # 复制文件
            shutil.copy2(source_path, target_path)
            
            # 刷新模型列表
            self.refresh_models_list()
            
            return True, f"模型 {new_name} 添加成功"
        except Exception as e:
            return False, f"添加模型失败: {str(e)}"
    
    def delete_model(self, model_name):
        """
        删除指定的模型
        :param model_name: 模型文件名
        :return: 是否删除成功
        """
        try:
            model_path = os.path.join(self.models_dir, model_name)
            
            # 检查文件是否存在
            if not os.path.exists(model_path):
                return False, f"模型文件不存在: {model_name}"
            
            # 如果模型已加载，从缓存中移除
            if model_name in self.models_cache:
                del self.models_cache[model_name]
            
            # 删除文件
            os.remove(model_path)
            
            # 从信息字典中移除
            if model_name in self.models_info:
                del self.models_info[model_name]
            
            return True, f"模型 {model_name} 删除成功"
        except Exception as e:
            return False, f"删除模型失败: {str(e)}"


# 创建28SY型电机的模型管理器实例
DIAGNOSIS_MODEL_28SY_DIR = os.path.join(settings.BASE_DIR, 'faultDiagnosis', 'models_28sy_v2')
model_manager_28sy = ModelManager(DIAGNOSIS_MODEL_28SY_DIR) 