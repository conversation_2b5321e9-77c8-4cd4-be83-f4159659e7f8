# Generated by Django 3.2.4 on 2021-07-17 07:11

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=30, verbose_name='邮箱')),
                ('nickname', models.Char<PERSON>ield(max_length=30, null=True, verbose_name='昵称')),
                ('pass_word', models.CharField(max_length=20, verbose_name='密码')),
                ('isLogin', models.BooleanField(default=False, verbose_name='is_login')),
                ('cookie', models.CharField(default='phm', max_length=30, verbose_name='cookie')),
                ('token', models.CharField(default='phm', max_length=30, verbose_name='token')),
                ('info', models.Char<PERSON><PERSON>(max_length=500, null=True, verbose_name='个人简介')),
                ('gender', models.IntegerField(choices=[(0, '男'), (1, '女')], null=True, verbose_name='性别')),
                ('phone', models.CharField(max_length=20, null=True, verbose_name='手机号')),
                ('qq', models.CharField(max_length=20, null=True, verbose_name='qq')),
                ('wechat', models.CharField(max_length=20, null=True, verbose_name='wechat')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
            },
        ),
    ]
