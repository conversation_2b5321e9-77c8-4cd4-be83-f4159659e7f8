# Generated by Django 3.2.16 on 2025-07-01 02:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='HealthAssessment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assessment_time', models.DateTimeField(auto_now_add=True, verbose_name='评估时间')),
                ('health_score', models.FloatField(verbose_name='健康度评分')),
                ('health_status', models.CharField(choices=[('good', '良好'), ('warning', '警告'), ('danger', '危险')], max_length=10, verbose_name='健康状态')),
                ('remarks', models.TextField(blank=True, null=True, verbose_name='备注')),
            ],
            options={
                'verbose_name': '健康评估记录',
                'verbose_name_plural': '健康评估记录',
                'ordering': ['-assessment_time'],
            },
        ),
        migrations.CreateModel(
            name='RULPrediction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('prediction_time', models.DateTimeField(auto_now_add=True, verbose_name='预测时间')),
                ('remaining_hours', models.FloatField(verbose_name='剩余使用寿命(小时)')),
                ('life_percentage', models.FloatField(verbose_name='寿命百分比')),
                ('confidence', models.FloatField(default=90.0, verbose_name='预测置信度')),
                ('assessment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='rul_prediction', to='health_assessment.healthassessment', verbose_name='健康评估')),
            ],
            options={
                'verbose_name': '寿命预测记录',
                'verbose_name_plural': '寿命预测记录',
                'ordering': ['-prediction_time'],
            },
        ),
        migrations.CreateModel(
            name='ComponentHealth',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('component_name', models.CharField(max_length=50, verbose_name='部件名称')),
                ('health_score', models.FloatField(verbose_name='健康度评分')),
                ('assessment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='components', to='health_assessment.healthassessment', verbose_name='健康评估')),
            ],
            options={
                'verbose_name': '部件健康记录',
                'verbose_name_plural': '部件健康记录',
            },
        ),
    ]
