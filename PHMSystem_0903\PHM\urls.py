from django.contrib import admin
from django.urls import path
from PHM.views import *
from User.views import *
from django.urls import include

app_name = 'phm'
urlpatterns = [
    path('upData/', upData, name="upData"),  # 客户端数据上传
    path('getData/', getNowData, name="getData"),  # 浏览器获取当前数据
    path('getDataByTime/', getDataByTime, name="getDataByTime"),# 浏览器获取历史数据
    path('getDataByTimeMonitor/', getDataByTimeMonitor, name="getDataByTimeMonitor"),  # 浏览器状态监测
    path('getDataBaseLastId/', getDataBaseLastId, name="getDataBaseLastId"),# 获取id
    path('getFile/', getFile, name="getFile"),  # 远程传输文件
    path('getModelp/', getModelp, name="getModelp"),  # 获取三维模型
    path('getFaultInject/', getFaultInject, name="getFaultInject"),  # 故障注入控制
    path('getFaultTree/', getFaultTree, name="getFaultTree"),  # 获取故障树
    path('getOfflineData/', getOfflineData, name="getOfflineData"),  # 离线故障诊断 - 获取数据
    path('getOfflineModel/', getOfflineModel, name="getOfflineModel"),  # 离线故障诊断 - 获取模型
    path('offlineDiagnosis/', offlineDiagnosis, name="offlineDiagnosis"),  # 离线故障诊断 - 获取模型
    path('test/',test,name="test"),
    path('getFaultIndex/', getFaultIndex, name="getFaultIndex"),
    path('get_ip/', get_ip, name="get_ip"),
    path('getOfflineData_28sy/', get_offline_data_28sy, name="getOfflineData_28sy"),
    path('getOfflineModel_28sy/', get_offline_model_28sy, name="getOfflineModel_28sy"),
    path('offlineDiagnosis_28sy/', offline_diagnosis_28sy, name="offlineDiagnosis_28sy"),
    path('uploadModel_28sy/', upload_model_28sy, name="uploadModel_28sy"),  # 上传新模型
    path('deleteModel_28sy/', delete_model_28sy, name="deleteModel_28sy"),  # 删除模型
    
    # 新增传感器数据实时模拟接口
    path('get_sensor_types/', get_sensor_types, name="get_sensor_types"),  # 获取可用的传感器类型
    path('start_data_collection/', start_data_collection, name="start_data_collection"),  # 开始数据采集
    path('stop_data_collection/', stop_data_collection, name="stop_data_collection"),  # 停止数据采集
    path('get_simulated_data/', get_simulated_data, name="get_simulated_data"),  # 获取模拟数据
    path('download_sensor_data/', download_sensor_data, name="download_sensor_data"),  # 下载传感器数据
    path('saveMonitorData/', save_monitor_data, name='saveMonitorData'),  # 保存设备状态监测数据到历史记录
    path('saveDiagnosisData/', save_diagnosis_data, name='saveDiagnosisData'),  # 保存故障诊断数据到历史记录
]