from django.core import serializers
from PHMSystem.HttpResonseMy import *
import numpy as np
from PHMSystem.HttpCode import *
import tensorflow as tf
from .models import *
from .SelfQueue import SelfQueue
from queue import Queue
from tensorflow.python.keras.backend import set_session
import os, json, time, shutil, keras
from django.utils import timezone
from typing import *
from django.conf import settings # 导入Django的settings
from faultDiagnosis.model_manager import model_manager_28sy  # 导入模型管理器
import random
from datetime import datetime
# json: 用于字符串和python数据类型间进行转换
# import gru_train_onlyfault
# from faultDiagnosis.gru_train_onlyfault import MyLstm,AAAAA

'''
每次upData请求，首先向数据库存储原数据data，然后另用变量getNowData存储原数据，解算出
三相电流、健康状态、故障状态，将这些数据存储在变量nowData中
每次getData请求，浏览器获取当时的nowData
'''

allSendNums = 0


errorDictJson = os.path.join(os.getcwd(), 'static\\json\\errorDict.json')
faultInjectedJson = os.path.join(os.getcwd(), 'static\\json\\faultInjected.json')
diagnosisModelJson = os.path.join(os.getcwd(), 'static\\json\\diagnosisModel.json')
allFaultDataDir = os.path.join(os.getcwd(), 'static\\allFaultData')
injectFaultDataDir = os.path.join(os.getcwd(), 'static\\injectFaultData')
diagnosisModelDir = os.path.join(os.getcwd(), 'static\\diagnosisModel')

# 程序开始时声明
tf.compat.v1.disable_eager_execution()
sess = tf.compat.v1.Session()
init = tf. compat.v1. global_variables_initializer()
sess. run(init)
graph = tf.compat.v1.get_default_graph()
set_session(sess)

dataQueueX = SelfQueue(512)
dataQueueY = SelfQueue(512)
dataQueueZ = SelfQueue(512)

data_len = 512
# 用于累计 XGET 小于 0.1的次数
healthCountNum = 0

# 正在传输的数据
nowData = {}
nowDataList = []
nowDataIndex = 0
# 控制多少个数据点进行诊断
diagnosisControlNum = 0

# 直接依靠幅值诊断正常和位移传感器故障
XgiveQueue = Queue(maxsize=512)
XgetQueue = Queue(maxsize=512)

# 连续多个点诊断结果相同时，才认定此种故障 faultList 存放诊断类别，达到20后，判断是否只有一类，是则将此类型存入nowFaultStatus
# 浏览器将取该值作为显示，默认为0.如果不是，则对该值不做改变
faultList = []
nowFaultStatus = 0

# 计数某些种类的诊断次数
fiveCount = 0
tenCount = 0
eighteenCount = 0
otherfaultCount =0
normalCount = 0
# 故障状态变量 初始为正常 当一次诊断后，全局变量改变，之后维持该值
faultStatus = 0

# 以下是被移除的远程控制相关变量
# controlSignalFlag = False
# controlData = ""

databaseLastId = 0

with graph.as_default():
    set_session(sess)
myLstm = None


class MyLSTMPredict():
    '''
    单独的一个预测类，直接读取数据，进行输入数据的故障预测
    '''
    def __init__(self,modelName,dataLen):
        '''
        初始化
        :param modelName: 模型的路径
        :param dataLen: 数据长度
        '''
        try:
            self.model = tf.keras.models.load_model(modelName, compile=False)
            self.dataLen = dataLen
            print(f"成功加载模型: {modelName}")
        except Exception as e:
            print(f"加载模型失败: {modelName}")
            print(f"错误信息: {e}")
            # 尝试加载备用模型
            backup_model = './static/diagnosisModel/2_epoch_200_acc_0.9275_time_2021_11_19_20_18.h5'
            if modelName != backup_model:
                try:
                    print(f"尝试加载备用模型: {backup_model}")
                    self.model = tf.keras.models.load_model(backup_model, compile=False)
                    self.dataLen = dataLen
                    print(f"成功加载备用模型: {backup_model}")
                except Exception as backup_e:
                    print(f"备用模型也加载失败: {backup_e}")
                    raise Exception("所有模型文件都无法加载，请检查模型文件完整性")
            else:
                raise Exception("模型文件损坏，请重新训练或获取完整的模型文件")

    def predictOneFaultClass(self,xData2):
        '''
        输入单组数据进行预测
        :param xData2: 单组数据 维度为(3,512)
        :return: 单组数据的预测结果 (10, '位移传感器故障')
        '''
        xData = []
        xData.append(xData2)
        xData = np.array(xData)
        xData = xData.reshape(xData.shape[0],xData.shape[1],self.dataLen // 128, 128)
        yGet = self.model.predict({'input1': xData[:, 0, :, :], 'input2': xData[:, 1, :, :], 'input3': xData[:, 2, :, :]}, batch_size=512)
        faultType = np.argmax(yGet, -1)
        return faultType[0]


myLstm = MyLSTMPredict('./static/diagnosisModel/2_epoch_300_acc_0.93775_time_2021_11_20_09_33.h5', 512)


# ===================================================================
# ==          为 "28SY 型" FMEA 诊断功能预加载模型                 ==
# ===================================================================
# 使用模型管理器加载默认模型
with graph.as_default():
    set_session(sess)
    # 尝试加载默认模型
    default_model_name = 'model_28sy_augmented.h5'
    fmea_28sy_model = model_manager_28sy.load_model(default_model_name)
    if fmea_28sy_model:
        print(f"Successfully loaded default FMEA model: {default_model_name}")
    else:
        # 如果默认模型不存在，尝试加载任何可用的模型
        models_list = model_manager_28sy.get_models_list()
        if models_list:
            first_model = models_list[0]['name']
            fmea_28sy_model = model_manager_28sy.load_model(first_model)
            print(f"Loaded alternative FMEA model: {first_model}")
        else:
            fmea_28sy_model = None
            print("No FMEA models available.")
# ===================================================================


# def diagnosis(data, **model):
def diagnosis(data):
    global myLstm, sess, graph
    # 使用try/except处理不同Python版本的兼容性
    try:
        # Python 3.3+推荐的计时函数
        start = time.perf_counter()
    except AttributeError:
        # 向后兼容Python 3.6
        start = time.clock()
        
    with graph.as_default():
        set_session(sess)
        faultType = myLstm.predictOneFaultClass(data)
        
    try:
        end = time.perf_counter()
    except AttributeError:
        end = time.clock()
        
    t = end - start
    return faultType


def getStatus(dataQueueX:SelfQueue,dataQueueY:SelfQueue,dataQueueZ:SelfQueue) -> str:
    """
    将队列转换成数组，同时进行故障诊断
    :param dataQueueX: X相的数据
    :param dataQueueY: Y相的数据 
    :param dataQueueZ: Z相的数据 
    :return: 故障与否的代号
    """
    data = np.array([dataQueueX.data, dataQueueY.data, dataQueueZ.data])
    # data = data.reshape((1, 3, 4, 128))
    faultType = diagnosis(data)
    print("get Status:", faultType)
    return faultType

def getNowData(request):
    '''
    浏览器获取当前软件上传的数据
    :param request:
    :return: 当前透传的数据加上一个故障检查状态
    '''
    global nowData
    if request.method == "GET":
        print("nowData",nowData)
        if nowData:
            return normalResponse("data", nowData)
        else:
            return errorResponse(SOFTWARE_CLIENT_ERROE)

def upData(request):
    '''
    客户端软件上传数据, 对nowData进行更新
    :param request:
    :return: 返回状态码
    '''
    global faultList, nowFaultStatus, fiveCount, tenCount, eighteenCount, otherfaultCount, normalCount
    global diagnosisControlNum, healthCountNum
    global nowData
    global controlData, controlSignalFlag
    global dataQueueX, dataQueueY, dataQueueZ
    global faultStatus
    global allSendNums

    if request.method == 'POST':
        print("POST Data ---", request.body.decode("utf-8"))
        allgetNowData = request.body.decode('utf-8')
        # if getNowData.startswith('['):
        #     getNowData = myReplace(getNowData, ',', ':')  # 用于接收到的数据非字典形式时，转换格式
        allgetNowData = eval(allgetNowData)  # str -> list
        for i in range(len(allgetNowData)):
            getNowData = allgetNowData[i]
            typestring = getNowData['type']
            date = getNowData['time']
            healthStatus = '0.83'
            XGet = getNowData['x_get']
            XGive = getNowData['x_give']
            FGet = getNowData['f_get']
            Iq = getNowData['Iq']
            Ia, Ib, Ic = tranlq2labc(Iq, XGet)
            # nowData['faultStatus'] = str(nowFaultStatus)
            nowData['time'] = date
            nowData['x_get'] = XGet
            nowData['x_give'] = XGive
            nowData['f_get'] = FGet
            nowData['Iq'] = Iq
            # nowData['healthStatus'] = '0.83'
            # dataQueueX.put(Ia)
            # dataQueueY.put(Ib)
            # dataQueueZ.put(Ic)
            # 连续10个点指令位移小于0.1时，判断为未工作
            if abs(float(XGet)) < 0.1:
                healthCountNum += 1
                if healthCountNum > 10:
                    nowData['healthStatus'] = '0'
            else:
                healthCountNum = 0
                nowData['healthStatus'] = '0.83'

            # if dataQueueX.full() and dataQueueY.full() and dataQueueZ.full():
            #     diagnosisControlNum += 1
            #     if diagnosisControlNum >= 10 and  healthCountNum < 10 :  # 每10个点诊断一次
            #         faultStatus = getStatus(dataQueueX, dataQueueY, dataQueueZ)
            #         # nowData['faultStatus'] = str(faultStatus)
            #         print('诊断类别', faultStatus)
            #         diagnosisControlNum = 0
            #         if faultStatus != 0:
            #             if faultStatus == 5:
            #                 fiveCount += 1
            #             elif faultStatus == 10:
            #                 tenCount += 1
            #             elif faultStatus == 18:
            #                 eighteenCount += 1
            #             else:
            #                 otherfaultCount += 1
            #             print('五十十八其', fiveCount, tenCount, eighteenCount, otherfaultCount)
            #         else:
            #             print('正常', normalCount)
            #
            #         faultList.append(faultStatus)
            #         print('12341', len(faultList), faultList)
            #         if len(faultList) == 20:
            #             if len(set(faultList)) == 1:
            #                 nowFaultStatus = faultStatus
            #             else:
            #                 pass
            #             faultList = []
            #         else:
            #             pass
            #         nowData['faultStatus'] = str(nowFaultStatus)
            XgetQueue.put(float(XGet))
            XgiveQueue.put(float(XGive))
            if XgetQueue.full() and XgiveQueue.full():
                diagnosisControlNum += 1
                if diagnosisControlNum >= 10 and  healthCountNum < 10:
                    if abs(max(list(XgetQueue.queue))) > 3 * abs(max(list(XgiveQueue.queue))):
                        faultStatus = 10
                    else:
                        faultStatus = 0
                    diagnosisControlNum = 0
                XgetQueue.get()
                XgiveQueue.get()

            faultList.append(faultStatus)
            if len(faultList) == 40:
                if len(set(faultList)) == 1:
                    nowFaultStatus = faultStatus
                else:
                    pass
                faultList = []
            else:
                pass
            nowFaultStatus = faultStatus
            print('诊断结果:', faultStatus)
            nowData['faultStatus'] = str(nowFaultStatus)

            nowDataStr = str(nowData)
            nowDataStr=nowDataStr.replace("'", '"')
            RunData.objects.create(mod_date=date, data=nowDataStr, faultStatus=faultStatus, healthStatus=healthStatus, type=typestring)
            allSendNums += 1
            print('共写入数据库', allSendNums)
    else:
        print('请求类型错误', request)
        return errorResponse(REQUEST_TYPE_ERROR)
    # 以当前的远程控制指令作为返回值
    if controlSignalFlag:
        controlSignalFlag = False
        return normalResponse('controlData', controlData)
    else:
        controlData = {"signalType": "stop", "signalNums": "0", "signalFre": "0", "signalAmp": "0"}
        return normalResponse('controlData', controlData)


def getDataByTime(request):
    '''
    通过时间段获取，当前满足条件的数据 获取历史数据
    :param request: 
    :return: 
    '''
    if request.method == "GET":
        try:
            data = {}
            startTime = request.GET['startTime']
            print(startTime)
            endTime = request.GET['endTime']
            print(endTime)
            
            # 构建基本查询条件
            query_conditions = {
                'mod_date__gt': startTime,
                'mod_date__lt': endTime
            }
            
            # 如果指定了数据类型，添加到查询条件中
            dataType = request.GET.get('dataType', None)
            if dataType:
                if dataType == 'monitor':
                    # 查询所有以monitor_开头的数据
                    query_conditions['type__startswith'] = 'monitor_'
                elif dataType == 'diagnosis':
                    # 查询诊断类型的数据
                    query_conditions['type'] = 'diagnosis'
            
            # 使用过滤条件查询数据
            getRunData = RunData.objects.filter(**query_conditions).order_by('-mod_date')
            print(getRunData)
            data['data'] = json.loads(serializers.serialize("json", getRunData))
            print(data['data'])
            for item in data['data']:
                print(item)
                item['fields']['mod_date'] = item['fields']['mod_date'].replace('T', ' ')
            print(data['data'])
            return JsonResponse(data)
        except Exception as e:
            print(f"获取历史数据出错: {str(e)}")
            return errorResponse(REQUEST_TYPE_ERROR)
    return errorResponse(REQUEST_TYPE_ERROR)

def getDataByTimeMonitor(request):
    '''
    通过时间段获取，当前满足条件的数据 获取历史数据
    :param request:
    :return:
    '''
    global databaseLastId
    if request.method == "GET":
        drawNum = eval(request.GET['requestNum'])
        data = {}
        # getRunData = RunData.objects.get(id=databaseLastId)
        getRunData = RunData.objects.filter(id__gt=databaseLastId, id__lte=databaseLastId + drawNum)
        if len(getRunData) == drawNum:
            databaseLastId += drawNum
        else:
            if len(getRunData) != 0:
                print('252', getRunData)
                databaseLastId = getRunData[len(getRunData)-1].id
        # getRunData =[getRunData]
        data['data'] = json.loads(serializers.serialize("json", getRunData))
        return JsonResponse(data)
    return errorResponse(REQUEST_TYPE_ERROR)

def getDataBaseLastId(request):
    global databaseLastId
    getLastData = RunData.objects.last()
    databaseLastId = getLastData.id
    print(databaseLastId)
    return normalResponse('id', databaseLastId)

def getModelp(request):
    if request.method == 'GET':
        type = request.GET['type']
        with open(os.path.join(os.getcwd(), 'static\\model\\', type+'.png'), 'rb') as f:
            ret_img_data = f.read()
        return HttpResponse(ret_img_data, content_type='image/png')

def getFaultInject(request):
    print("正在运行getFaultInject")
    with open(errorDictJson, 'r', encoding='utf-8') as load_f_EN:
        load_EN_dict = json.load(load_f_EN)  # loads() 将json对象解析为python对象
    if request.method == 'GET':
        faultType = request.GET['faultType']
        signalFre = request.GET['signalFre']
        faultLine = request.GET['faultLine']
        injectTime = time.strftime("%Y_%m_%d_%H_%M_%S", time.localtime())
        faultEN = load_EN_dict['errorIndex2ENameDict'][faultType]
        oriName = faultEN + '-fre-' + signalFre.split('Hz')[0]
        nowName = oriName + '_' + injectTime + '.npz'
        if not os.path.exists(injectFaultDataDir):
            os.makedirs(injectFaultDataDir)
        shutil.copy(os.path.join(allFaultDataDir, oriName + '.npz'), os.path.join(injectFaultDataDir, nowName))
        return normalResponse('faultInject：', faultType)
    else:
        return errorResponse(FORM_ERROR)

def checkFileWriteJson(sourceDir, targetJson):
    number = 1
    fileDict = {}
    for root, dirs, files in os.walk(sourceDir):
        for i in files:
            if i.endswith('.npz') or i.endswith('.h5'):
                fileDict[str(number) + str(i.split('.')[-1])] = i
                number += 1
    with open(targetJson, 'w') as f:  # 写入json文件
        json.dump(fileDict, f, indent=4, ensure_ascii=False)

def test(request):
    RunData.objects.create(data="12345",status = "2")
    return HttpResponse("OK")

def getFaultTree(request):
    if request.method == "GET":
        type = request.GET['type']
        try:
            data = {}
            with open(os.path.join(os.getcwd(), 'static\\json\\faultTree.json'), 'r', encoding='utf-8') as load_f:
                load_dict = json.load(load_f)
                if type == 'pump':
                    faultTree = load_dict['pumpTreeData']
                else :
                    faultTree = load_dict['emaTreeData']
            data['data'] = faultTree
            return JsonResponse(data, safe=False)
        except:
            return errorResponse(REQUEST_TYPE_ERROR)
    return errorResponse(REQUEST_TYPE_ERROR)

def getFaultIndex(request):
    if request.method == "GET":
        try:
            data = {}
            with open(errorDictJson, 'r', encoding='utf-8') as load_f:
                load_dict = json.load(load_f)
                faultIndex = load_dict['errorName2IndexDict']
            data['data'] = faultIndex
            return JsonResponse(data, safe=False)
        except:
            return errorResponse(REQUEST_TYPE_ERROR)
    return errorResponse(REQUEST_TYPE_ERROR)

def getOfflineData(request):
    checkFileWriteJson(injectFaultDataDir, faultInjectedJson)
    if request.method == "GET":
        try:
            data = {}
            with open(faultInjectedJson, 'r', encoding='utf-8') as load_f:
                load_dict = json.load(load_f)
            data['data'] = load_dict
            return JsonResponse(data, safe=False)
        except:
            return errorResponse(REQUEST_TYPE_ERROR)
    return errorResponse(REQUEST_TYPE_ERROR)

def getOfflineModel(request):
    checkFileWriteJson(diagnosisModelDir, diagnosisModelJson)
    if request.method == "GET":
        try:
            data = {}
            with open(diagnosisModelJson, 'r', encoding='utf-8') as load_f:
                load_dict = json.load(load_f)
            data['data'] = load_dict
            return JsonResponse(data, safe=False)
        except:
            return errorResponse(REQUEST_TYPE_ERROR)
    return errorResponse(REQUEST_TYPE_ERROR)


def offlineDiagnosis(request):
    global data_len
    with open(errorDictJson, 'r', encoding='utf-8') as load_f:
        load_dict = json.load(load_f)  # loads() 将json对象解析为python对象
    if request.method == "GET":
        # try:
        offlineData = request.GET['offlineData']
        offlineDataName = offlineData.split('-fre')[0]
        supposedFaultType = load_dict['errorDict'][offlineDataName]
        supposedFaultList = np.array([supposedFaultType for i in range(50)])
        offlineModel = request.GET['offlineModel']
        data = np.load(os.path.join(injectFaultDataDir, offlineData))
        data = data['data']
        diagnosisNodeList= [i * 50 for i in range(50)]
        timeList = [str(i*50) for i in range(50)]
        statusList =[]
        strStatusList = []
        for i in range(len(diagnosisNodeList)):
            dataList = []
            dataList.append(np.array(data[3][diagnosisNodeList[i]: diagnosisNodeList[i] + data_len]))
            dataList.append(np.array(data[4][diagnosisNodeList[i]: diagnosisNodeList[i] + data_len]))
            dataList.append(np.array(data[5][diagnosisNodeList[i]: diagnosisNodeList[i] + data_len]))
            statusList.append(diagnosis(np.array(dataList)))

        conclusion = countRepeatMax(statusList)
        result = (supposedFaultList == np.array(statusList)).astype('int')
        acc = np.sum(result)/50
        for i in range(len(statusList)):
            strStatusList.append(str(statusList[i]))
        data = {}
        a = []
        for i in range(len(statusList)):
            a.append({'time': timeList[i], 'status': strStatusList[i]})
        data['code'] = 200
        data['data'] = a
        data['conclusion'] = int(conclusion)
        return JsonResponse(data, safe=False)
    return errorResponse(REQUEST_TYPE_ERROR)



# 远程控制功能已移除
# def sendControlSignal(request):
#     '''
#     上传远程控制命令
#     :param request: 指令信号的类型 幅值 频率等
#     :return: 是否成功
#     '''
#     已移除

def get_ip(request):
    '''
    获取请求者的IP信息
    :param request:
    :return: 请求者的ip
    '''
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')  # 判断是否使用代理
    if x_forwarded_for:
        a = '1'
        ip = x_forwarded_for.split(',')[0]  # 使用代理获取真实的ip
    else:
        a = '0'
        ip = request.META.get('REMOTE_ADDR')  # 未使用代理获取IP
    return "ip："+ ip


def tranlq2labc(Iq, Xs):
    Iq = float(Iq)
    Xs = float(Xs)
    p = 4
    ls = 0.00254
    thetaE = 2*np.pi*p/ls*Xs
    Ia = round(-np.sin(thetaE)*Iq, 3)
    Ib = round(-np.sin(thetaE-2*np.pi/3)*Iq, 3)
    Ic = round(-np.sin(thetaE + 2*np.pi/3), 3)
    return Ia, Ib, Ic

def myReplace(myStr, splitType, newType):
    myStr = myStr.replace('[', '{')
    myStr = myStr.replace(']', '}')
    myStr = myStr.replace('"', "'")
    strList = myStr.split(splitType)
    strNew = ''
    for i in range(len(strList)):
        if i == len(strList)-1:
            strNew += strList[i]
        else:
            if i%2 == 0:
                strNew += strList[i]
                strNew += newType
            elif i%2 ==1:
                strNew += strList[i]
                strNew += splitType
    return strNew

def countRepeatMax(inputList):
    noRepeat_list = list(set(inputList))
    countList = []
    countDist = {}
    for i in range(len(noRepeat_list)):
        countList.append(inputList.count(noRepeat_list[i]))
        countDist[noRepeat_list[i]] = inputList.count(noRepeat_list[i])
    print(countDist)
    return list(countDist.keys())[list(countDist.values()).index(max(countList))]

def getFile(request):
    res = {'path': os.path.join(os.getcwd(), 'static')}
    return JsonResponse(res)

def saveFaultInjected(nowName):
    shutil.copy(os.path.join(allFaultDataDir, nowName), injectFaultDataDir)
    return 'ok'

# ===================================================================
# ==                                                               ==
# ==      以下是为 "28SY 型" 电机新增的 FMEA 诊断功能相关接口        ==
# ==                                                               ==
# ===================================================================

# --- 新增常量 ---
# 使用 settings.BASE_DIR 来构建绝对路径，这是最健壮的方式
# SIMULATED_DATA_28SY_DIR = os.path.join(settings.BASE_DIR, 'static', 'simulated_data_npz_augmented')
# !! 更新：指向100%诊断正确的数据集 !!
SIMULATED_DATA_28SY_DIR = os.path.join(settings.BASE_DIR, 'static', 'simulated_data_npz_perfect')
DIAGNOSIS_MODEL_28SY_DIR = os.path.join(settings.BASE_DIR, 'faultDiagnosis', 'models_28sy_v2')

# 新模型的固定文件名
MODEL_28SY_FILENAME = 'model_28sy_augmented.h5'

# FMEA 14种模式的标签定义 (与训练脚本保持一致)
FMEA_28SY_TARGET_NAMES = [
    '0_normal', '1_degradation_magnet', '2_degradation_brush_wear', 
    '3_degradation_commutator_oxidation', '4_fault_stator_short', 
    '5_fault_rotor_open', '6_degradation_bearing_wear', '7_fault_bearing_stuck',
    '8_degradation_gear_wear', '9_degradation_sensor_drift', '10_fault_sensor_loss',
    '11_fault_mosfet_breakdown', '12_degradation_drive_distortion', '13_fault_mcu_crash'
]

# --- 新增视图函数 ---

def get_offline_data_28sy(request):
    """
    获取用于 28SY 型电机离线诊断的所有可用数据(.npz)文件列表。
    """
    if request.method == "GET":
        try:
            if not os.path.exists(SIMULATED_DATA_28SY_DIR):
                os.makedirs(SIMULATED_DATA_28SY_DIR)
            
            all_files = os.listdir(SIMULATED_DATA_28SY_DIR)
            npz_files = [f for f in all_files if f.endswith('.npz')]
            
            # 按文件名中的数字前缀排序
            npz_files.sort(key=lambda x: int(x.split('_')[0]))

            return normalResponse("data", npz_files)
        except Exception as e:
            return errorResponse(code=SERVER_ERROR, data=str(e))

def get_offline_model_28sy(request):
    """
    获取用于 28SY 型电机离线诊断的可用模型文件列表。
    """
    if request.method == "GET":
        try:
            # 使用模型管理器获取模型列表
            models = model_manager_28sy.get_models_list()
            return normalResponse("data", models)
        except Exception as e:
            return errorResponse(code=SERVER_ERROR, data=str(e))

def offline_diagnosis_28sy(request):
    """
    执行 28SY 型电机离线故障诊断的核心接口。
    接收一个数据文件名和模型名称，返回包含详细诊断步骤的 JSON 对象。
    """
    if request.method == 'GET':
        data_filename = request.GET.get('name')
        model_name = request.GET.get('model', '')  # 可选参数，如果未提供则使用默认模型
        
        if not data_filename:
            return errorResponse(code=BAD_REQUEST, data="缺少 'name' 参数 (数据文件名)")

        # 初始化诊断报告
        report = {
            "success": False,
            "message": "",
            "diagnosis_details": {
                "model_used": model_name if model_name else "default",
                "data_file": data_filename,
                "steps": [],
                "conclusion": {}
            }
        }
        
        # --- 步骤 1: 加载指定的模型或默认模型 ---
        step1_time = time.time()
        
        global fmea_28sy_model
        with graph.as_default():
            set_session(sess)
            
            # 如果指定了模型名称，尝试加载该模型
            if model_name:
                model = model_manager_28sy.load_model(model_name)
                if model:
                    fmea_28sy_model = model  # 更新全局模型变量
                    report["diagnosis_details"]["steps"].append({
                        "step": 1,
                        "description": f"加载指定模型: {model_name}",
                        "status": "成功",
                        "duration_ms": round((time.time() - step1_time) * 1000)
                    })
                else:
                    report["message"] = f"指定的模型 {model_name} 加载失败"
                    return JsonResponse(report)
            else:
                # 使用已加载的默认模型
                if fmea_28sy_model is None:
                    report["message"] = "没有可用的诊断模型"
                    return JsonResponse(report)
                
                report["diagnosis_details"]["steps"].append({
                    "step": 1,
                    "description": "使用默认诊断模型",
                    "status": "成功",
                    "duration_ms": round((time.time() - step1_time) * 1000)
                })
            
            model = fmea_28sy_model

        # --- 步骤 2: 加载并预处理数据 ---
        step2_time = time.time()
        data_path = os.path.join(SIMULATED_DATA_28SY_DIR, data_filename)
        if not os.path.exists(data_path):
            report["message"] = f"数据文件未找到: {data_path}"
            return JsonResponse(report)
        
        try:
            raw_data = np.load(data_path)
            # 与训练脚本完全一致的预处理流程
            channels = [raw_data['position'], raw_data['current'], raw_data['setpoint']]
            stacked_data = np.stack(channels, axis=1)
            
            mean = np.mean(stacked_data, axis=0)
            std = np.std(stacked_data, axis=0)
            std[std == 0] = 1 # 避免除以零
            standardized_data = (stacked_data - mean) / std
            
            sequence_length = 256 # 与训练时一致
            num_segments = len(standardized_data) // sequence_length
            segments = []
            for i in range(num_segments):
                start = i * sequence_length
                end = start + sequence_length
                segments.append(standardized_data[start:end])
            
            X_test = np.array(segments)

            report["diagnosis_details"]["steps"].append({
                "step": 2,
                "description": "加载并预处理诊断数据",
                "status": "成功",
                "duration_ms": round((time.time() - step2_time) * 1000),
                "details": {"segments_created": X_test.shape[0]}
            })
        except Exception as e:
            report["message"] = f"处理数据失败: {str(e)}"
            return JsonResponse(report)

        # --- 步骤 3: 模型推理 ---
        step3_time = time.time()
        try:
            with graph.as_default():
                set_session(sess)
                predictions = model.predict(X_test)
            report["diagnosis_details"]["steps"].append({
                "step": 3,
                "description": "执行模型推理",
                "status": "成功",
                "duration_ms": round((time.time() - step3_time) * 1000)
            })
        except Exception as e:
            report["message"] = f"模型推理失败: {str(e)}"
            return JsonResponse(report)

        # --- 步骤 4: 汇总结果并生成结论 ---
        step4_time = time.time()
        try:
            # 计算所有片段的平均概率分布
            avg_probabilities = np.mean(predictions, axis=0)
            # 找到最高概率的索引和值
            final_prediction_index = np.argmax(avg_probabilities)
            confidence_score = float(avg_probabilities[final_prediction_index])
            
            # 获取最终结论的标签名
            predicted_fault_mode = FMEA_28SY_TARGET_NAMES[final_prediction_index]
            
            # 构建完整的概率分布列表
            full_probability_distribution = [
                {"mode": name, "probability": float(prob)} 
                for name, prob in zip(FMEA_28SY_TARGET_NAMES, avg_probabilities)
            ]
            
            report["diagnosis_details"]["conclusion"] = {
                "predicted_fault_mode": predicted_fault_mode,
                "confidence_score": round(confidence_score, 4),
                "full_probability_distribution": full_probability_distribution
            }
            report["diagnosis_details"]["steps"].append({
                "step": 4,
                "description": "汇总诊断结果",
                "status": "成功",
                "duration_ms": round((time.time() - step4_time) * 1000)
            })

            # --- 最终报告 ---
            report["success"] = True
            report["message"] = "诊断完成"
            return JsonResponse(report, safe=False)

        except Exception as e:
            report["message"] = f"结果汇总失败: {str(e)}"
            return JsonResponse(report)

# 新增API接口：上传新模型
def upload_model_28sy(request):
    """
    处理新模型的上传和添加
    """
    if request.method == 'POST':
        try:
            # 检查是否有文件上传
            if 'model_file' not in request.FILES:
                return errorResponse(code=BAD_REQUEST, data="没有上传模型文件")
            
            model_file = request.FILES['model_file']
            
            # 检查文件类型
            if not model_file.name.endswith('.h5'):
                return errorResponse(code=BAD_REQUEST, data="只支持.h5格式的模型文件")
            
            # 临时保存上传的文件到faultDiagnosis文件夹中
            temp_dir = os.path.join(settings.BASE_DIR, 'faultDiagnosis', 'temp')
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
                
            temp_path = os.path.join(temp_dir, model_file.name)
            
            with open(temp_path, 'wb+') as destination:
                for chunk in model_file.chunks():
                    destination.write(chunk)
            
            # 获取新名称（如果提供）
            new_name = request.POST.get('new_name', None)
            
            # 添加模型到模型目录
            success, message = model_manager_28sy.add_model(temp_path, new_name)
            
            # 删除临时文件
            os.remove(temp_path)
            
            if success:
                return normalResponse("message", message)
            else:
                return errorResponse(code=SERVER_ERROR, data=message)
                
        except Exception as e:
            return errorResponse(code=SERVER_ERROR, data=str(e))
    
    return errorResponse(code=REQUEST_TYPE_ERROR)

# 新增API接口：删除模型
def delete_model_28sy(request):
    """
    删除指定的模型文件
    """
    if request.method == 'POST':
        try:
            model_name = request.POST.get('model_name')
            if not model_name:
                return errorResponse(code=BAD_REQUEST, data="缺少模型名称参数")
            
            success, message = model_manager_28sy.delete_model(model_name)
            
            if success:
                return normalResponse("message", message)
            else:
                return errorResponse(code=SERVER_ERROR, data=message)
                
        except Exception as e:
            return errorResponse(code=SERVER_ERROR, data=str(e))
    
    return errorResponse(code=REQUEST_TYPE_ERROR)

# ===================================================================
# ==          新增传感器数据实时模拟接口                            ==
# ===================================================================
# 全局变量，用于控制数据采集状态
is_collecting_data = False
current_sensor_type = "position"  # 默认传感器类型
current_data_index = 0  # 当前数据索引
simulated_data_buffer = []  # 模拟数据缓冲区
current_batch_size = 5  # 默认每次返回5个数据点
current_data_file = None  # 当前使用的数据文件
last_data_point = None  # 上一个数据点的值，用于确保连续性

def get_sensor_types(request):
    """
    获取可用的传感器类型
    """
    if request.method == "GET":
        # 根据npz文件中的键返回可用的传感器类型
        sensor_types = ["position", "current", "setpoint"]
        return JsonResponse({
            'code': 200,
            'data': {
                'sensor_types': sensor_types
            }
        }, json_dumps_params={'ensure_ascii': False})
    return errorResponse(REQUEST_METHOD_ERROR)

def start_data_collection(request):
    """
    开始数据采集
    """
    global is_collecting_data, current_data_index, simulated_data_buffer, current_sensor_type, current_batch_size
    global current_data_file, last_data_point
    
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            sensor_type = data.get("sensor_type", "position")
            
            # 检查是否切换了传感器类型
            sensor_changed = current_sensor_type != sensor_type
            current_sensor_type = sensor_type
            
            # 获取采样频率参数，根据采样频率调整批次大小
            sampling_rate = data.get("sampling_rate", 100)  # 默认100ms (10Hz)
            
            # 根据采样频率调整批次大小，确保数据流畅
            # 采样频率越高，每次返回的数据点越少，以避免数据积累过快
            if sampling_rate <= 50:  # 20Hz或更高
                current_batch_size = 1
            elif sampling_rate <= 100:  # 10Hz
                current_batch_size = 2
            elif sampling_rate <= 200:  # 5Hz
                current_batch_size = 3
            elif sampling_rate <= 500:  # 2Hz
                current_batch_size = 5
            else:  # 1Hz或更低
                current_batch_size = 8
            
            # 重置数据索引和缓冲区
            current_data_index = 0
            simulated_data_buffer = []
            
            # 如果切换了传感器类型，重置数据文件选择
            if sensor_changed:
                current_data_file = None
                last_data_point = None
            
            is_collecting_data = True
            
            return normalResponse("status", "数据采集已开始")
        except Exception as e:
            return errorResponse(SERVER_ERROR, str(e))
    return errorResponse(REQUEST_METHOD_ERROR)

def stop_data_collection(request):
    """
    停止数据采集
    """
    global is_collecting_data, current_data_index, simulated_data_buffer
    global current_data_file, last_data_point
    
    if request.method == "POST":
        is_collecting_data = False
        # 保留当前数据文件选择，但清空缓冲区和索引
        simulated_data_buffer = []
        current_data_index = 0
        last_data_point = None
        return normalResponse("status", "数据采集已停止")
    return errorResponse(REQUEST_METHOD_ERROR)

def get_simulated_data(request):
    """
    获取模拟数据
    """
    global is_collecting_data, current_data_index, simulated_data_buffer, current_sensor_type, current_batch_size
    global current_data_file, last_data_point
    
    if request.method == "GET":
        if not is_collecting_data:
            return normalResponse("data", {"status": "not_collecting", "values": []})
        
        # 从静态文件夹中选择npz文件
        simulated_data_dir = os.path.join(os.getcwd(), 'static', 'simulated_data_npz_augmented')
        
        # 确保目录存在
        if not os.path.exists(simulated_data_dir):
            os.makedirs(simulated_data_dir)
            # 如果目录是新创建的，返回一个错误提示
            return errorResponse(SERVER_ERROR, "模拟数据目录不存在，已创建空目录，请添加数据文件")
        
        npz_files = [f for f in os.listdir(simulated_data_dir) if f.endswith('.npz')]
        
        if not npz_files:
            return errorResponse(REQUEST_METHOD_ERROR, "无可用的模拟数据文件")
        
        # 如果缓冲区为空，则加载新数据
        if not simulated_data_buffer:
            # 选择一个文件 - 如果之前已经有选择，则继续使用该文件
            if current_data_file is None or current_data_file not in npz_files:
                current_data_file = random.choice(npz_files)
            
            file_path = os.path.join(simulated_data_dir, current_data_file)
            
            # 确保文件存在
            if not os.path.exists(file_path):
                return errorResponse(SERVER_ERROR, f"数据文件不存在: {file_path}")
            
            try:
                # 加载npz文件
                data = np.load(file_path)
                
                # 确保所需的传感器类型存在
                if current_sensor_type not in data:
                    # 尝试查找可用的传感器类型
                    available_keys = list(data.keys())
                    if available_keys:
                        current_sensor_type = available_keys[0]  # 使用第一个可用的传感器类型
                        print(f"所选传感器类型不存在，使用可用的传感器类型: {current_sensor_type}")
                    else:
                        return errorResponse(REQUEST_METHOD_ERROR, f"数据文件中没有可用的传感器数据")
                
                # 获取传感器数据并转换为列表
                sensor_data = data[current_sensor_type].tolist()
                
                # 只取部分数据用于模拟，确保数据量足够
                buffer_size = 1000  # 增加缓冲区大小，减少循环频率
                if len(sensor_data) > buffer_size:
                    simulated_data_buffer = sensor_data[:buffer_size]
                else:
                    # 如果数据不够，则重复数据以达到所需长度
                    repeats = buffer_size // len(sensor_data) + 1
                    simulated_data_buffer = (sensor_data * repeats)[:buffer_size]
                
                # 为数据添加适度的随机噪声，保持连续性
                add_realistic_noise(simulated_data_buffer, current_sensor_type)
                
                # 重置索引
                current_data_index = 0
                last_data_point = None
                
            except Exception as e:
                return errorResponse(SERVER_ERROR, f"加载数据文件时出错: {str(e)}")
        
        # 检查是否需要重新加载数据（接近缓冲区末尾）
        if current_data_index >= len(simulated_data_buffer) - current_batch_size * 2:
            # 保存最后一个点的值，用于确保连续性
            last_data_point = simulated_data_buffer[-1]
            
            # 重新加载数据，但不重置current_data_file，保持使用同一个文件
            simulated_data_buffer = []
            return get_simulated_data(request)  # 递归调用自身重新加载数据
        
        # 每次返回一小批数据点
        batch_size = current_batch_size
        end_index = min(current_data_index + batch_size, len(simulated_data_buffer))
        
        # 获取当前批次的数据
        current_batch = simulated_data_buffer[current_data_index:end_index]
        
        # 生成时间戳
        timestamps = [timezone.now().strftime("%H:%M:%S.%f")[:-3] for _ in range(len(current_batch))]
        
        # 更新数据索引
        current_data_index = end_index
        
        # 返回数据和时间戳
        response_data = {
            "status": "collecting",
            "sensor_type": current_sensor_type,
            "values": current_batch,
            "timestamps": timestamps
        }
        
        return normalResponse("data", response_data)
    
    return errorResponse(REQUEST_METHOD_ERROR)

def add_realistic_noise(data_buffer, sensor_type):
    """
    为数据添加真实的随机噪声，保持数据连续性
    """
    global last_data_point
    
    # 根据不同传感器类型添加不同特性的噪声
    if sensor_type == "position":
        # 位置传感器通常有较小的高频噪声
        noise_level = 0.002  # 降低噪声幅度，避免突变
        trend_noise = 0.001  # 低频漂移噪声
    elif sensor_type == "current":
        # 电流传感器通常有较大的脉冲噪声
        noise_level = 0.008  # 降低噪声幅度
        trend_noise = 0.002  # 低频漂移噪声
    elif sensor_type == "setpoint":
        # 设定值通常有较小的噪声
        noise_level = 0.0005  # 降低噪声幅度，使正弦波更平滑
        trend_noise = 0.0002  # 低频漂移噪声
    else:
        # 默认噪声水平
        noise_level = 0.003  # 降低噪声幅度
        trend_noise = 0.001  # 低频漂移噪声
    
    # 计算信号的平均幅度
    signal_mean = np.mean(np.abs(data_buffer))
    if signal_mean < 0.001:  # 避免除以接近零的值
        signal_mean = 0.001
    
    # 如果有上一个数据点，确保连续性
    if last_data_point is not None:
        # 调整第一个点，使其与上一个数据点连续
        # 添加一个小的随机变化，但保持在合理范围内
        small_change = np.random.normal(0, noise_level * signal_mean * 0.5)
        data_buffer[0] = last_data_point + small_change
    
    # 添加高频随机噪声（白噪声），但保持连续性
    prev_noise = 0
    for i in range(len(data_buffer)):
        # 高斯白噪声，但与前一个噪声有一定关联，确保平滑过渡
        white_noise = prev_noise * 0.7 + np.random.normal(0, noise_level * signal_mean) * 0.3
        
        # 低频漂移噪声（使用正弦函数模拟）
        drift = trend_noise * signal_mean * np.sin(i * 0.02)
        
        # 不再添加突发性脉冲噪声，避免数据突变
        
        # 将噪声添加到原始数据
        data_buffer[i] += white_noise + drift
        
        # 更新前一个噪声值
        prev_noise = white_noise

def download_sensor_data(request):
    """
    下载传感器数据为Excel格式
    """
    from openpyxl import Workbook
    from django.http import HttpResponse
    
    if request.method == "POST":
        try:
            # 解析请求数据
            data = json.loads(request.body)
            sensor_type = data.get("sensor_type", "position")
            values = data.get("values", [])
            timestamps = data.get("timestamps", [])
            
            if not values or not timestamps:
                return errorResponse(BAD_REQUEST, "没有可下载的数据")
            
            # 创建Excel工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = f"{sensor_type}_data"
            
            # 添加标题行
            ws.append(["时间戳", "数值"])
            
            # 添加数据行
            for timestamp, value in zip(timestamps, values):
                ws.append([timestamp, value])
            
            # 创建响应
            response = HttpResponse(
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            )
            response["Content-Disposition"] = f'attachment; filename="{sensor_type}_data_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'
            
            # 保存工作簿到响应
            wb.save(response)
            
            return response
        except Exception as e:
            return errorResponse(SERVER_ERROR, f"下载数据时出错: {str(e)}")
    
    return errorResponse(REQUEST_METHOD_ERROR)

def save_monitor_data(request):
    """
    保存设备状态监测数据到历史记录
    :param request: POST请求，包含timestamp、value、sensor_type、status、health_status
    :return: JSON响应
    """
    if request.method == "POST":
        try:
            # 解析请求数据
            data = json.loads(request.body)
            timestamp = data.get('timestamp')
            value = data.get('value')
            sensor_type = data.get('sensor_type', '')
            status = data.get('status', '0')  # 默认正常状态
            health_status = data.get('health_status', '1')  # 默认健康状态
            
            # 转换时间戳为datetime对象
            if isinstance(timestamp, str):
                # 如果是ISO格式字符串，转换为datetime
                if 'T' in timestamp:
                    mod_date = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                else:
                    # 假设是其他格式的字符串
                    mod_date = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
            else:
                # 如果是数字，假设是毫秒时间戳
                mod_date = datetime.fromtimestamp(timestamp / 1000.0)
            
            # 创建RunData记录
            RunData.objects.create(
                mod_date=mod_date,
                data=str(value),
                faultStatus=status,
                healthStatus=health_status,
                type=f"monitor_{sensor_type}"  # 标记数据来源
            )
            
            return normalResponse("保存监测数据成功", "success")
        except Exception as e:
            return errorResponse(f"保存监测数据失败: {str(e)}")
    return errorResponse(REQUEST_TYPE_ERROR)

def save_diagnosis_data(request):
    """
    保存故障诊断数据到历史记录
    :param request: POST请求，包含timestamp、fault_mode、data_file、model_used、status、health_status
    :return: JSON响应
    """
    if request.method == "POST":
        try:
            # 解析请求数据
            data = json.loads(request.body)
            timestamp = data.get('timestamp')
            fault_mode = data.get('fault_mode', '')
            data_file = data.get('data_file', '')
            model_used = data.get('model_used', '')
            status = data.get('status', '0')  # 默认正常状态
            health_status = data.get('health_status', '1')  # 默认健康状态
            
            # 输出调试信息
            print(f"保存诊断数据: timestamp={timestamp}, fault_mode={fault_mode}")
            print(f"data_file={data_file}, model_used={model_used}")
            print(f"status={status}, health_status={health_status}")
            
            # 转换时间戳为datetime对象
            mod_date = None
            if isinstance(timestamp, str):
                try:
                    # 如果是ISO格式字符串，转换为datetime
                    if 'T' in timestamp:
                        mod_date = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    else:
                        # 假设是其他格式的字符串
                        mod_date = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                except Exception as e:
                    # 如果转换失败，使用当前时间
                    print(f"时间戳转换失败: {e}")
                    mod_date = timezone.now()
            else:
                # 如果是数字，假设是毫秒时间戳
                try:
                    mod_date = datetime.fromtimestamp(timestamp / 1000.0)
                except Exception as e:
                    # 如果转换失败，使用当前时间
                    print(f"时间戳转换失败: {e}")
                    mod_date = timezone.now()
            
            # 如果上面所有转换都失败，使用当前时间
            if mod_date is None:
                mod_date = timezone.now()
                
            print(f"转换后的时间: {mod_date}")
            
            # 创建RunData记录
            record = RunData.objects.create(
                mod_date=mod_date,
                data=f"{data_file}|{model_used}|{fault_mode}",  # 把诊断相关信息编码到data字段
                faultStatus=status,
                healthStatus=health_status,
                type="diagnosis"  # 标记数据来源
            )
            
            print(f"保存成功，记录ID: {record.id}")
            
            return normalResponse("保存诊断数据成功", "success")
        except Exception as e:
            print(f"保存诊断数据失败: {str(e)}")
            return errorResponse(f"保存诊断数据失败: {str(e)}")
    return errorResponse(REQUEST_TYPE_ERROR)