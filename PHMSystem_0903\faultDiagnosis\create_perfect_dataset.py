import os
import numpy as np
import tensorflow as tf
import shutil
import sys

# --- 配置路径 ---
# 确保脚本可以找到Django项目设置
# 将项目根目录添加到Python路径
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.dirname(BASE_DIR))

# 现在可以安全地导入Django设置
from PHMSystem_0903.PHMSystem import settings

# 源数据目录 (包含280个增强样本)
SOURCE_DATA_DIR = os.path.join(settings.BASE_DIR, 'static', 'simulated_data_npz_augmented')
# 目标数据目录 (只存放100%诊断正确的样本)
TARGET_DATA_DIR = os.path.join(settings.BASE_DIR, 'static', 'simulated_data_npz_perfect')
# 模型文件路径
MODEL_PATH = os.path.join(settings.BASE_DIR, 'faultDiagnosis', 'models_28sy_v2', 'model_28sy_augmented.h5')

# FMEA 14种模式的标签定义 (必须与训练和视图中的定义完全一致)
FMEA_28SY_TARGET_NAMES = [
    '0_normal', '1_degradation_magnet', '2_degradation_brush_wear', 
    '3_degradation_commutator_oxidation', '4_fault_stator_short', 
    '5_fault_rotor_open', '6_degradation_bearing_wear', '7_fault_bearing_stuck',
    '8_degradation_gear_wear', '9_degradation_sensor_drift', '10_fault_sensor_loss',
    '11_fault_mosfet_breakdown', '12_degradation_drive_distortion', '13_fault_mcu_crash'
]

def preprocess_data(data_path, sequence_length=256):
    """
    加载并预处理单个NPZ文件，返回可供模型输入的测试数据。
    该函数逻辑必须与 `views.py` 中的 `offline_diagnosis_28sy` 保持完全一致。
    """
    raw_data = np.load(data_path)
    # 提取三个通道的数据
    channels = [raw_data['position'], raw_data['current'], raw_data['setpoint']]
    stacked_data = np.stack(channels, axis=1)
    
    # 标准化
    mean = np.mean(stacked_data, axis=0)
    std = np.std(stacked_data, axis=0)
    std[std == 0] = 1 # 避免除以零
    standardized_data = (stacked_data - mean) / std
    
    # 分割成段
    num_segments = len(standardized_data) // sequence_length
    segments = []
    for i in range(num_segments):
        start = i * sequence_length
        end = start + sequence_length
        segments.append(standardized_data[start:end])
    
    return np.array(segments)

def get_true_label_from_filename(filename):
    """
    从文件名中提取真实的故障模式标签。
    例如: '1_degradation_magnet_sample_0.npz' -> '1_degradation_magnet'
    """
    parts = filename.split('_')
    # 结合前缀数字和故障描述
    true_label_name = f"{parts[0]}_{'_'.join(parts[1:-2])}"
    if true_label_name in FMEA_28SY_TARGET_NAMES:
        return true_label_name
    return None

def main_batched():
    """
    主执行函数 - 采用高效的批处理版本
    """
    print("--- 开始筛选100%诊断准确的数据集 (高效批处理模式) ---")
    
    # 1. 加载模型
    print(f"正在加载模型: {MODEL_PATH}")
    if not os.path.exists(MODEL_PATH):
        print(f"[错误] 模型文件未找到，请检查路径。")
        return
    model = tf.keras.models.load_model(MODEL_PATH, compile=False)
    print("模型加载成功。")

    # 2. 检查并清空目标目录
    if not os.path.exists(TARGET_DATA_DIR):
        os.makedirs(TARGET_DATA_DIR)
        print(f"创建目标目录: {TARGET_DATA_DIR}")
    else:
        print(f"目标目录已存在，将清空其中的内容...")
        for f in os.listdir(TARGET_DATA_DIR):
            os.remove(os.path.join(TARGET_DATA_DIR, f))

    # 3. 遍历所有文件，一次性加载和预处理
    source_files = [f for f in os.listdir(SOURCE_DATA_DIR) if f.endswith('.npz')]
    if not source_files:
        print(f"[错误] 源数据目录 {SOURCE_DATA_DIR} 中没有找到 .npz 文件。")
        return
    
    print(f"正在从 {len(source_files)} 个文件中加载并预处理所有数据段...")
    
    all_segments = []
    file_segment_info = [] # 存储 (filename, true_label, num_segments)

    for filename in source_files:
        true_label = get_true_label_from_filename(filename)
        if not true_label:
            print(f"\n[警告] 无法从文件名 {filename} 中解析出有效标签，已跳过。")
            continue

        file_path = os.path.join(SOURCE_DATA_DIR, filename)
        segments = preprocess_data(file_path)

        if segments.shape[0] > 0:
            all_segments.append(segments)
            file_segment_info.append({
                "filename": filename,
                "true_label": true_label,
                "num_segments": segments.shape[0]
            })
    
    if not all_segments:
        print("[错误] 未能从任何文件中加载有效数据。")
        return

    # 将所有段拼接成一个大的批次
    X_batch = np.vstack(all_segments)
    print(f"数据加载完成，总共 {X_batch.shape[0]} 个数据段。")

    # 4. 一次性执行模型推理
    print("开始对整个批次进行模型推理 (这可能会花费一些时间)...")
    # 使用较大的batch_size以提升GPU/CPU效率
    all_predictions = model.predict(X_batch, batch_size=256, verbose=1)
    print("推理完成。")

    # 5. 遍历结果，筛选并复制文件
    print("正在分析诊断结果并筛选文件...")
    perfect_count = 0
    perfect_files_by_class = {label: 0 for label in FMEA_28SY_TARGET_NAMES}
    segment_start_index = 0

    for info in file_segment_info:
        num_segments = info["num_segments"]
        true_label = info["true_label"]
        filename = info["filename"]
        
        # 提取该文件对应的所有预测结果
        file_predictions = all_predictions[segment_start_index : segment_start_index + num_segments]
        
        # 计算平均概率
        avg_probabilities = np.mean(file_predictions, axis=0)
        predicted_index = np.argmax(avg_probabilities)
        predicted_label = FMEA_28SY_TARGET_NAMES[predicted_index]

        # 检查是否诊断正确
        if predicted_label == true_label:
            perfect_count += 1
            perfect_files_by_class[true_label] += 1
            # 复制文件到目标目录
            shutil.copy(os.path.join(SOURCE_DATA_DIR, filename), os.path.join(TARGET_DATA_DIR, filename))
        
        # 更新下一个文件段的起始索引
        segment_start_index += num_segments

    # 6. 打印最终报告
    print("\n--- 筛选完成 ---")
    print(f"总文件数: {len(source_files)}")
    print(f"100%诊断正确的文件数: {perfect_count}")
    print(f"筛选成功率: {perfect_count / len(source_files) * 100:.2f}%")
    print("\n各类别筛选出的文件数量:")
    for label, count in perfect_files_by_class.items():
        print(f"- {label}: {count} 个")
    print(f"\n所有诊断正确的文件已复制到: {TARGET_DATA_DIR}")

if __name__ == "__main__":
    main_batched() 