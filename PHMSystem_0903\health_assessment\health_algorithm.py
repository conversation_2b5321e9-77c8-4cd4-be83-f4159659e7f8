import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout

class HealthAssessmentAlgorithm:
    """健康评估算法类"""
    
    def __init__(self):
        """初始化健康评估算法"""
        # 部件权重配置（可根据实际情况调整）
        self.component_weights = {
            '电机': 0.35,
            '控制器': 0.30,
            '减速器': 0.20,
            '传感器': 0.15
        }
        
        # 健康状态阈值
        self.health_thresholds = {
            'good': 80.0,  # 80分以上为良好
            'warning': 60.0  # 60-80分为警告，60分以下为危险
        }
    
    def evaluate_health(self, sensor_data, diagnosis_result=None):
        """
        评估系统健康度
        
        参数:
            sensor_data: 传感器数据，包含各个部件的参数
            diagnosis_result: 故障诊断结果，可选
            
        返回:
            health_score: 健康度评分 (0-100)
            health_status: 健康状态 ('good', 'warning', 'danger')
            component_health: 各部件健康度评分的字典
        """
        # 初始化部件健康度字典
        component_health = {}
        
        # 计算各部件健康度
        for component, weight in self.component_weights.items():
            # 实际开发中，需根据传感器数据和故障诊断结果计算各部件健康度
            # 这里使用模拟数据进行演示
            if diagnosis_result and component in diagnosis_result:
                # 如果有故障诊断结果，则将对应部件的健康度调低
                fault_severity = diagnosis_result[component].get('severity', 0)
                component_score = max(0, 100 - fault_severity * 20)
            else:
                # 否则，根据传感器数据估计健康度
                # 这里使用简化的模型，实际应用中应该有更复杂的算法
                base_score = 85 + np.random.normal(0, 5)  # 基础分
                
                # 模拟不同传感器数据对健康度的影响
                if component == '电机':
                    if 'motor_temp' in sensor_data:
                        # 电机温度过高会降低健康度
                        temp_penalty = max(0, (sensor_data['motor_temp'] - 60) * 0.5)
                        base_score -= temp_penalty
                        
                    if 'motor_vibration' in sensor_data:
                        # 振动过大会降低健康度
                        vib_penalty = max(0, (sensor_data['motor_vibration'] - 0.5) * 10)
                        base_score -= vib_penalty
                
                # 其他部件的健康度计算逻辑类似，根据实际情况添加
                
                component_score = max(0, min(100, base_score))
            
            component_health[component] = component_score
        
        # 计算整体健康度评分（加权平均）
        total_weight = sum(self.component_weights.values())
        weighted_sum = sum(component_health[comp] * self.component_weights[comp] 
                          for comp in component_health 
                          if comp in self.component_weights)
        
        health_score = weighted_sum / total_weight if total_weight > 0 else 0
        
        # 确定健康状态
        if health_score >= self.health_thresholds['good']:
            health_status = 'good'
        elif health_score >= self.health_thresholds['warning']:
            health_status = 'warning'
        else:
            health_status = 'danger'
        
        return {
            'health_score': health_score,
            'health_status': health_status,
            'component_health': component_health
        }


class RULPredictionAlgorithm:
    """剩余使用寿命预测算法类"""
    
    def __init__(self):
        """初始化寿命预测算法"""
        self.model = None
        self.scaler = None
        self.max_lifetime_hours = 10000  # 设备最大寿命（小时）
        
        # 初始化模型
        self._init_model()
    
    def _init_model(self):
        """初始化预测模型（可以是随机森林或LSTM）"""
        # 这里使用RandomForest作为示例
        # 实际应用中应该根据数据特点选择合适的模型并进行训练
        self.model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        
        # 注意：在实际应用中，应该从保存的模型文件中加载预训练模型
        # self.model = load_model('path/to/model')
    
    def _init_lstm_model(self, input_shape):
        """初始化LSTM模型"""
        model = Sequential()
        model.add(LSTM(units=50, return_sequences=True, input_shape=input_shape))
        model.add(Dropout(0.2))
        model.add(LSTM(units=50))
        model.add(Dropout(0.2))
        model.add(Dense(units=1))
        model.compile(optimizer='adam', loss='mean_squared_error')
        return model
    
    def prepare_features(self, history_data, current_data):
        """
        准备模型输入特征
        
        参数:
            history_data: 历史运行数据
            current_data: 当前状态数据
            
        返回:
            features: 模型输入特征
        """
        # 合并历史数据和当前数据
        all_data = history_data.append(current_data, ignore_index=True)
        
        # 提取相关特征
        features = []
        
        # 1. 统计特征
        for column in ['vibration', 'temperature', 'pressure', 'current']:
            if column in all_data.columns:
                # 计算均值、标准差、最大值、最小值等统计特征
                features.append(all_data[column].mean())
                features.append(all_data[column].std())
                features.append(all_data[column].max())
                features.append(all_data[column].min())
        
        # 2. 趋势特征
        for column in ['vibration', 'temperature']:
            if column in all_data.columns:
                # 计算最近数据的趋势斜率
                recent_data = all_data[column].tail(10).values
                if len(recent_data) >= 2:
                    slope = np.polyfit(np.arange(len(recent_data)), recent_data, 1)[0]
                    features.append(slope)
                else:
                    features.append(0)
        
        # 3. 故障特征
        if 'fault_count' in all_data.columns:
            features.append(all_data['fault_count'].sum())
        
        # 将特征转换为numpy数组
        features = np.array(features).reshape(1, -1)
        
        # 特征标准化
        if self.scaler is not None:
            try:
                features = self.scaler.transform(features)
            except:
                # 如果scaler未训练，则先进行训练
                self.scaler.fit(features)
                features = self.scaler.transform(features)
        
        return features
    
    def predict_rul(self, features, health_score):
        """
        预测剩余使用寿命
        
        参数:
            features: 模型输入特征
            health_score: 健康度评分
            
        返回:
            remaining_hours: 剩余使用寿命（小时）
            life_percentage: 寿命百分比
            confidence: 预测置信度
        """
        # 如果模型未训练，则使用基于健康度的简单计算
        if self.model is None or not hasattr(self.model, 'predict'):
            # 基于健康度的简单计算公式
            # 健康度为100时，剩余寿命为最大寿命
            # 健康度为0时，剩余寿命为0
            remaining_hours = (health_score / 100.0) * self.max_lifetime_hours
            confidence = 75.0  # 简单估计的置信度较低
        else:
            try:
                # 使用预训练模型进行预测
                remaining_hours = self.model.predict(features)[0]
                # 计算预测置信度（这是一个简化的方法，实际应用中需要更复杂的计算）
                confidence = 90.0
            except:
                # 如果模型预测失败，则使用简单计算
                remaining_hours = (health_score / 100.0) * self.max_lifetime_hours
                confidence = 70.0
        
        # 确保预测结果在合理范围内
        remaining_hours = max(0, min(remaining_hours, self.max_lifetime_hours))
        
        # 计算寿命百分比
        life_percentage = (remaining_hours / self.max_lifetime_hours) * 100.0
        
        return {
            'remaining_hours': remaining_hours,
            'life_percentage': life_percentage,
            'confidence': confidence
        }

    def get_health_trend(self, days=30):
        """
        获取历史健康度趋势数据
        
        参数:
            days: 获取多少天的数据
            
        返回:
            trend_data: 健康度趋势数据，格式为 [(日期, 健康度), ...]
        """
        # 在实际应用中，应该从数据库中读取历史健康度记录
        # 这里使用模拟数据
        
        trend_data = []
        today = datetime.now()
        
        for i in range(days, -1, -1):
            date = today - timedelta(days=i)
            date_str = date.strftime('%Y-%m-%d')
            
            # 模拟健康度数据，略微下降的趋势
            base_health = 90 - (i / days) * 10
            random_factor = np.random.normal(0, 2)
            health = max(0, min(100, base_health + random_factor))
            
            trend_data.append((date_str, health))
        
        return trend_data 