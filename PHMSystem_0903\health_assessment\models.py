from django.db import models

# Create your models here.

class HealthAssessment(models.Model):
    """系统健康评估记录"""
    
    # 评估时间
    assessment_time = models.DateTimeField(auto_now_add=True, verbose_name="评估时间")
    
    # 整体健康度评分 (0-100)
    health_score = models.FloatField(verbose_name="健康度评分")
    
    # 健康状态 (良好/警告/危险)
    HEALTH_STATUS_CHOICES = (
        ('good', '良好'),
        ('warning', '警告'),
        ('danger', '危险'),
    )
    health_status = models.CharField(max_length=10, choices=HEALTH_STATUS_CHOICES, verbose_name="健康状态")
    
    # 额外备注
    remarks = models.TextField(null=True, blank=True, verbose_name="备注")
    
    class Meta:
        verbose_name = "健康评估记录"
        verbose_name_plural = verbose_name
        ordering = ['-assessment_time']
    
    def __str__(self):
        return f"健康评估 {self.assessment_time} - {self.health_score}分"


class ComponentHealth(models.Model):
    """关键部件健康状态记录"""
    
    # 关联的健康评估记录
    assessment = models.ForeignKey(HealthAssessment, on_delete=models.CASCADE, related_name="components", verbose_name="健康评估")
    
    # 部件名称
    component_name = models.CharField(max_length=50, verbose_name="部件名称")
    
    # 部件健康度评分 (0-100)
    health_score = models.FloatField(verbose_name="健康度评分")
    
    class Meta:
        verbose_name = "部件健康记录"
        verbose_name_plural = verbose_name
    
    def __str__(self):
        return f"{self.component_name} - {self.health_score}分"


class RULPrediction(models.Model):
    """剩余使用寿命预测记录"""
    
    # 预测时间
    prediction_time = models.DateTimeField(auto_now_add=True, verbose_name="预测时间")
    
    # 关联的健康评估记录
    assessment = models.OneToOneField(HealthAssessment, on_delete=models.CASCADE, related_name="rul_prediction", verbose_name="健康评估")
    
    # 预测的剩余使用寿命（小时）
    remaining_hours = models.FloatField(verbose_name="剩余使用寿命(小时)")
    
    # 寿命百分比 (0-100)
    life_percentage = models.FloatField(verbose_name="寿命百分比")
    
    # 预测置信度 (0-100)
    confidence = models.FloatField(default=90.0, verbose_name="预测置信度")
    
    class Meta:
        verbose_name = "寿命预测记录"
        verbose_name_plural = verbose_name
        ordering = ['-prediction_time']
    
    def __str__(self):
        return f"寿命预测 {self.prediction_time} - {self.remaining_hours}小时"
