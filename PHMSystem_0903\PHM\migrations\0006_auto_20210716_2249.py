# Generated by Django 3.2.4 on 2021-07-16 14:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('PHM', '0005_user_token'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='admission_time',
        ),
        migrations.RemoveField(
            model_name='user',
            name='auth',
        ),
        migrations.RemoveField(
            model_name='user',
            name='email',
        ),
        migrations.RemoveField(
            model_name='user',
            name='graduation_destination',
        ),
        migrations.RemoveField(
            model_name='user',
            name='graduation_time',
        ),
        migrations.RemoveField(
            model_name='user',
            name='icon',
        ),
        migrations.RemoveField(
            model_name='user',
            name='qq',
        ),
        migrations.RemoveField(
            model_name='user',
            name='status',
        ),
        migrations.RemoveField(
            model_name='user',
            name='student_number',
        ),
        migrations.RemoveField(
            model_name='user',
            name='wx',
        ),
        migrations.Alter<PERSON>ield(
            model_name='user',
            name='cookie',
            field=models.CharField(default='phm', max_length=30, verbose_name='cookie'),
        ),
        migrations.AlterField(
            model_name='user',
            name='info',
            field=models.CharField(max_length=500, verbose_name='个人简介'),
        ),
        migrations.AlterField(
            model_name='user',
            name='name',
            field=models.CharField(max_length=30, verbose_name='邮箱'),
        ),
        migrations.AlterField(
            model_name='user',
            name='pass_word',
            field=models.CharField(max_length=20, verbose_name='密码'),
        ),
        migrations.AlterField(
            model_name='user',
            name='token',
            field=models.CharField(default='phm', max_length=30, verbose_name='token'),
        ),
    ]
