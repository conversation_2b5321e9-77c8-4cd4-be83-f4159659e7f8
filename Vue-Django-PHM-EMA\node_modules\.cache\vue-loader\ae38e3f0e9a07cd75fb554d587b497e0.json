{"remainingRequest": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\home\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\src\\views\\home\\index.vue", "mtime": 1753423044588}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1634626726238}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1634626843626}, {"path": "E:\\hyxd_software\\PHM-agument\\Vue-Django-PHM-EMA\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1634627893245}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/home", "sourcesContent": ["<template>\n  <div class=\"home-container\">\n    <div class=\"headtxt\">\n      <h3\n        style=\"font-family: KaiTi;font-size:1.55em;font-weight:bold;\n        margin-top:15px;margin-bottom:5px\"\n      >\n        伺服系统 PHM 软件平台</h3>\n    </div>\n    <div id=\"model-container\">\n      <div class=\"ctl\">\n        <el-button-group>\n          <el-button @click=\"onWindowResize\">刷新3D视图</el-button>\n        </el-button-group>\n      </div>\n      <div v-show=\"infoShow\" id=\"infoBox\">\n        <ul style=\"font-size:20px; line-height:31px\">\n          <li>组件名称: {{ info.name }}</li>\n        </ul>\n      </div>\n      <!-- 伺服系统信息铭牌 -->\n      <div id=\"systemInfoBox\">\n        <h4>伺服系统信息</h4>\n        <ul>\n          <li><strong>型号:</strong> XXXX-EMA</li>\n          <li><strong>额定功率:</strong> 200W</li>\n          <li><strong>额定电压:</strong> 24V DC</li>\n          <li><strong>额定转速:</strong> 3000RPM</li>\n          <li><strong>控制方式:</strong> 闭环位置控制</li>\n          <li><strong>生产日期:</strong> 2023-05-15</li>\n          <li><strong>序列号:</strong> SN20230515001</li>\n        </ul>\n      </div>\n\n\n    </div>\n\n    <!-- 故障提示浮窗组件 -->\n    <fault-notification\n      :visible.sync=\"faultNotificationVisible\"\n      :type=\"faultNotificationType\"\n      :part-name=\"faultNotificationPartName\"\n      :status-name=\"faultNotificationStatusName\"\n      :diagnosis-time=\"faultNotificationTime\"\n      @close=\"handleFaultNotificationClose\"\n      @view-details=\"handleViewDetails\"\n    />\n  </div>\n</template>\n\n<script>\nimport * as THREE from 'three'\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'\nimport { STLLoader } from 'three/examples/jsm/loaders/STLLoader.js'\nimport dat from 'three/examples/js/libs/dat.gui.min.js'\nimport ModelInteractionUtil, {\n  DEFAULT_COLOR,\n  PART_COLOR,\n  GREEN_PARTS\n} from '@/utils/ModelInteractionUtil'\nimport RocketFlameEffect from '@/utils/RocketFlameEffect'\nimport FaultNotification from '@/components/FaultNotification.vue'\nimport {\n  getPartNameByFaultType,\n  getFaultTypeName,\n  getPartChineseName,\n  getStatusType\n} from '@/utils/faultToPartMapping'\n\nexport default {\n  name: '主控台', // eslint-disable-line vue/name-property-casing\n  components: {\n    FaultNotification\n  },\n  data() {\n    return {\n      dataShow: {\n        Xgive: '',\n        Xget: '',\n        Fget: '',\n        healthStatus: ''\n      },\n      dataPermit: false,\n      btntxt: '开始接收数据',\n      // mesh: null,\n      module: null,\n      moduleAll: null,\n      folderName: './3D/',\n      stlName: '',\n      materialCo: DEFAULT_COLOR,\n      camera: null,\n      scene: null,\n      renderer: null,\n      controls: null,\n      infoShow: true,\n      stateShow: true,\n      files: null,\n      info: {\n        name: ''\n      },\n      isFault: false,\n      state: {\n        all: 20,\n        normal: 20,\n        fault: 0\n      },\n      objectStatus: {\n        status: 0\n      },\n      drawFunc: '整体模型',\n      select: '',\n      clickCurrentColor: '',\n      faultCurrentColor: '',\n      p2hDict: null,\n      hanziList: [],\n      selectObject: null,\n      faultObject: null,\n      faultIndex: '',\n      faultObjectName: ['电机_R', '传感器', '控制器'],\n      errorNameDict: {},\n      statusName: '',\n      // 新增模型交互工具实例\n      modelInteraction: null,\n\n      // 火箭尾焰特效相关\n      rocketFlameEffect: null,\n\n      // 故障提示浮窗相关数据\n      faultNotificationVisible: false,\n      faultNotificationType: 'fault', // 'fault' 或 'degradation'\n      faultNotificationPartName: '',\n      faultNotificationStatusName: '',\n      faultNotificationTime: '',\n\n      // 当前故障/退化状态\n      currentFaultType: null,\n      currentFaultPart: null\n    }\n  },\n  mounted() {\n    this.init()\n    const element = document.getElementById('model-container')\n    this.files = require.context('../../../public/3D', false, /.STL$/).keys()\n    this.m = this.$notify.warning({\n      title: '提示',\n      message: '模型正在加载，请稍后进行操作...',\n      duration: 0,\n      showClose: false\n    })\n    // this.onWindowResize()\n    this.loadPre()\n    element.addEventListener('click', this.onMouseClick, false)\n    window.addEventListener('resize', this.onWindowResize, false)\n  },\n  activated() {\n    console.log('进入主控台页面了')\n    // 在页面激活时检查诊断结果\n    this.checkDiagnosisResult()\n  },\n  deactivated() {\n    console.log('离开主控台页面了')\n  },\n  beforeDestroy() {\n    clearInterval(this.timer)\n\n    // 清理火箭尾焰特效\n    if (this.rocketFlameEffect) {\n      this.rocketFlameEffect.destroy()\n      this.rocketFlameEffect = null\n    }\n\n    // 移除事件监听器\n    const element = document.getElementById('model-container')\n    if (element) {\n      element.removeEventListener('click', this.onMouseClick, false)\n    }\n    window.removeEventListener('resize', this.onWindowResize, false)\n  },\n  methods: {\n    // 整体初始化\n    init() {\n      this.createScene()\n      this.helper()\n      // this.initGui()\n      this.createLight()\n      this.createCamera()\n      this.createRender()\n      this.createControls()\n      this.render()\n\n      // 创建一个组来包含所有模型，并设置整体位置\n      this.moduleAll = new THREE.Group()\n      // 将模型放置在网格中央\n      this.moduleAll.position.set(0, 0, 0)\n      this.scene.add(this.moduleAll)\n\n      // 初始化模型交互工具，添加状态变化回调\n      this.modelInteraction = new ModelInteractionUtil(\n        this.scene,\n        this.info,\n        this.handleModelStatusChange\n      )\n\n      // 应用新的颜色设置\n      this.modelInteraction.resetAllModelsColor()\n\n      // 检查Vuex中是否有未处理的诊断结果\n      this.checkDiagnosisResult()\n    },\n\n    // 初始化火箭尾焰特效\n    initRocketFlameEffect() {\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (daodanModel) {\n        try {\n          this.rocketFlameEffect = new RocketFlameEffect(this.scene, daodanModel, {\n            intensity: 1,    // 固定强度值\n            speed: 3,        // 固定速度值\n            turbulence: 1    // 固定湍流值\n          })\n          console.log('火箭尾焰特效已初始化')\n        } catch (error) {\n          console.error('初始化火箭尾焰特效失败:', error)\n        }\n      }\n    },\n\n\n\n    // 检查Vuex中是否有未处理的诊断结果\n    checkDiagnosisResult() {\n      console.log('检查诊断结果', this.$store.getters['diagnosis/hasUnprocessedResult'])\n      if (this.$store.getters['diagnosis/hasUnprocessedResult']) {\n        const result = this.$store.getters['diagnosis/currentDiagnosisResult']\n        console.log('发现未处理的诊断结果:', result)\n        this.handleDiagnosisResult(result)\n        this.$store.dispatch('diagnosis/processDiagnosisResult')\n        console.log('诊断结果处理完成')\n      } else {\n        console.log('没有未处理的诊断结果')\n      }\n    },\n\n    // 处理模型状态变化\n    handleModelStatusChange(statusData) {\n      console.log('模型状态变化:', statusData)\n\n      if (statusData.type === 'fault' || statusData.type === 'degradation') {\n        // 更新状态计数\n        this.state.fault = 1\n        this.state.normal = this.state.all - this.state.fault\n        this.isFault = statusData.type === 'fault'\n\n        // 获取部件中文名称\n        const partChineseName = getPartChineseName(statusData.object.name)\n        console.log('部件中文名称:', partChineseName)\n\n        // 更新状态名称\n        this.statusName = statusData.info.statusName\n        console.log('状态名称:', this.statusName)\n\n        // 显示故障提示浮窗\n        this.showFaultNotification({\n          type: statusData.type,\n          partName: partChineseName,\n          statusName: statusData.info.statusName,\n          diagnosisTime: statusData.info.diagnosisTime\n        })\n\n        console.log('已显示故障提示浮窗')\n      } else {\n        // 重置状态\n        this.state.fault = 0\n        this.state.normal = this.state.all\n        this.isFault = false\n        this.statusName = ''\n        console.log('已重置状态')\n      }\n    },\n\n    // 显示故障提示浮窗\n    showFaultNotification(notificationData) {\n      console.log('显示故障提示浮窗:', notificationData)\n      this.faultNotificationType = notificationData.type\n      this.faultNotificationPartName = notificationData.partName\n      this.faultNotificationStatusName = notificationData.statusName\n      this.faultNotificationTime = notificationData.diagnosisTime\n      this.faultNotificationVisible = true\n\n      // 确保浮窗在DOM更新后显示\n      this.$nextTick(() => {\n        console.log('浮窗显示状态:', this.faultNotificationVisible)\n      })\n    },\n\n    // 处理故障提示浮窗关闭\n    handleFaultNotificationClose() {\n      this.faultNotificationVisible = false\n    },\n\n    // 处理故障诊断结果\n    handleDiagnosisResult(result) {\n      console.log('处理诊断结果:', result)\n\n      // 如果诊断成功\n      if (result && result.success) {\n        const diagnosisDetails = result.diagnosis_details\n        const conclusion = diagnosisDetails.conclusion\n        const faultType = conclusion.predicted_fault_mode\n\n        console.log('诊断结论:', faultType)\n\n        // 如果是正常状态，重置所有部件状态\n        if (faultType === '0_normal') {\n          console.log('诊断结果为正常状态，重置所有部件')\n          this.resetAllPartsStatus()\n          return\n        }\n\n        // 获取对应的部件名称\n        const partName = getPartNameByFaultType(faultType)\n        if (!partName) {\n          console.warn('未找到对应的部件:', faultType)\n          return\n        }\n\n        console.log('对应的部件名称:', partName)\n\n        // 获取状态类型和状态名称\n        const statusType = getStatusType(faultType)\n        const statusName = getFaultTypeName(faultType)\n\n        console.log('状态类型:', statusType, '状态名称:', statusName)\n\n        // 获取当前时间作为诊断时间\n        const diagnosisTime = new Date().toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit',\n          second: '2-digit',\n          hour12: false\n        })\n\n        // 保存当前故障信息\n        this.currentFaultType = faultType\n        this.currentFaultPart = partName\n\n        // 根据状态类型标记部件\n        if (statusType === 'fault') {\n          // 故障状态，标记为红色\n          console.log('标记部件为故障状态:', partName)\n          this.modelInteraction.markAsFault(partName, {\n            statusName,\n            diagnosisTime\n          })\n        } else if (statusType === 'degradation') {\n          // 退化状态，标记为褐色\n          console.log('标记部件为退化状态:', partName)\n          this.modelInteraction.markAsDegradation(partName, {\n            statusName,\n            diagnosisTime\n          })\n        }\n      } else {\n        // 诊断失败\n        console.error('诊断失败:', result ? result.message : '未知错误')\n      }\n    },\n\n    // 重置所有部件状态\n    resetAllPartsStatus() {\n      if (this.currentFaultPart) {\n        this.modelInteraction.resetStatus(this.currentFaultPart)\n        this.currentFaultPart = null\n        this.currentFaultType = null\n\n        // 重置状态计数\n        this.state.fault = 0\n        this.state.normal = this.state.all\n        this.isFault = false\n        this.statusName = ''\n      }\n    },\n\n    // 测试故障诊断\n    testFaultDiagnosis() {\n      console.log('测试故障诊断')\n\n      // 创建一个模拟的诊断结果\n      const mockDiagnosisResult = {\n        success: true,\n        diagnosis_details: {\n          conclusion: {\n            predicted_fault_mode: '4_fault_stator_short'\n          }\n        }\n      }\n\n      // 处理诊断结果\n      this.handleDiagnosisResult(mockDiagnosisResult)\n    },\n\n    // 三大件 创建场景\n    createScene() {\n      this.scene = new THREE.Scene()\n    },\n\n    // 创建相机\n    createCamera() {\n      const element = document.getElementById('model-container')\n      const k = element.clientWidth / element.clientHeight // 实际3d显示窗口宽高比\n      this.camera = new THREE.PerspectiveCamera(45, k, 0.1, 10000) // 透视相机 (for,aspect,near,far) 视场 长宽比 多近开始渲染 最远看到的距离\n      // 调整相机位置，以便更好地观察模型\n      this.camera.position.set(0, 400, 600)\n      this.camera.lookAt(new THREE.Vector3(0, 0, 0))\n      this.scene.add(this.camera)\n    },\n\n    // 创建渲染器\n    createRender() {\n      const element = document.getElementById('model-container')\n      this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })\n      this.renderer.setSize(element.clientWidth, element.clientHeight) // 设置渲染区域尺寸\n      this.renderer.shadowMap.enabled = true\n      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap\n      this.renderer.setClearColor(0x050505, 0.6)\n      element.appendChild(this.renderer.domElement) // 渲染div到canvas\n    },\n\n    // 渲染\n    render() {\n      // 确保导弹壳体始终保持透明\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (daodanModel && daodanModel.material && this.modelInteraction) {\n        this.modelInteraction.updateDaodanMaterial(daodanModel)\n      }\n\n      // 更新火箭尾焰特效\n      if (this.rocketFlameEffect) {\n        this.rocketFlameEffect.update()\n      }\n\n      this.renderer.render(this.scene, this.camera)\n      requestAnimationFrame(this.render)\n    },\n\n    // 底部网格和三轴指示器\n    helper() {\n      var gridplane = new THREE.GridHelper(1200, 60, 0xFF7F50, 0x4A4A4A) // 一格20\n      this.scene.add(gridplane)\n      const axes = new THREE.AxesHelper(200)\n      this.scene.add(axes)\n    },\n\n    // 控制模型组件\n    initGui() {\n      this.paras = {\n        // rotationSpeed: 0.005 // 中文也行\n        rotationSpeed: 0 // 中文也行\n      }\n      var gui = new dat.GUI({ autoPlace: false })\n      gui.domElement.id = 'gui'\n      document.getElementById('gui_container').appendChild(gui.domElement)\n      gui.add(this.paras, 'rotationSpeed', 0, 0.05)\n    },\n\n    // 创建光源\n    createLight() {\n      // 环境光 没有特定的光源，该光源不会影响阴影的产生，被应用到全局范围内的所有对象,使用该光源是为了弱化阴影或者添加一些颜色\n      const ambientLight = new THREE.AmbientLight(0x999999)\n      this.scene.add(ambientLight)\n      const point = new THREE.PointLight(0xffffff)\n      // const point = new THREE.SpotLight(0xffffff)\n      point.position.set(-300, 600, -400)\n      this.scene.add(point)\n    },\n\n    // 轨道控制、旋转缩放\n    createControls() {\n      this.controls = new OrbitControls(this.camera, this.renderer.domElement)\n    },\n\n    // 接收数据控制\n    dataGet() {\n      if (this.btntxt === '停止接收数据') {\n        this.btntxt = '开始接收数据'\n        this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n        if (this.faultCurrentColor === 14423100) {\n          this.faultObject.material.color.set(0x7CFC00)\n        } else {\n          this.faultObject.material.color.set(this.faultCurrentColor)\n        }\n      } else {\n        this.btntxt = '停止接收数据'\n        // 目前认为故障组件只有EMA总装一种，提前指定故障组件 应该在getData()中按照faultIndex再确定\n        this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n        this.faultCurrentColor = this.faultObject.material.color.getHex()\n        console.log('inital faultObjectColor', this.faultCurrentColor)\n        this.$axios.get('./errorDict.json').then(res => {\n          this.errorNameDict = res.data.errorNameDict\n          // console.log('字典', res.data.errorNameDict)\n        })\n      }\n      this.dataPermit = !this.dataPermit\n      console.log(this.dataPermit)\n      this.getData()\n    },\n\n    // 接收数据\n    getData() {\n      if (this.dataPermit) {\n        /* this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n        this.faultCurrentColor = this.faultObject.material.color.getHex() */\n        this.timer = setInterval(() => {\n          this.$axios.get('/phm/getData/').then((res) => {\n            // console.log('111222', res.data.data, typeof (res.data.data)) 类型是object\n            this.dataShow.Xget = res.data.data.x_get\n            this.dataShow.Xgive = res.data.data.x_give\n            this.dataShow.Fget = res.data.data.f_get\n            // cw, ch, r, k, x, y, z\n            this.dataPanel(1600, 840, 50, 6, -380, 0, 210)\n            this.faultIndex = res.data.data.faultStatus\n            this.dataShow.healthStatus = res.data.data.healthStatus\n            this.statusName = this.errorNameDict[this.faultIndex]\n            console.log('faultIndex', this.faultIndex)\n            if (this.faultIndex !== '0') {\n              this.isFault = true\n              this.objectStatus.status = 1\n              this.state.fault = 1\n              this.state.normal = 19\n              // if (this.faultIndex !== '10') {\n              //   this.faultObject = this.scene.getObjectByName(this.faultObjectName[0])\n              // }\n              this.faultObject.material.color.set(0xDC143C)\n            } else {\n              this.isFault = false\n              this.objectStatus.status = 0\n              this.faultObject.material.color.set(this.faultCurrentColor)\n              this.state.fault = 0\n              this.state.normal = 20\n            }\n          })\n        }, this.$Common.requestInterval)\n      } else {\n        clearInterval(this.timer)\n      }\n    },\n\n    // 加载STL模型\n    loadSTL(stlName) {\n      console.log('加载STL模型:', stlName)\n      // const THIS = this // eslint-disable-line no-unused-vars\n      const loader = new STLLoader()\n      loader.load(\n        this.folderName + stlName,\n        geometry => {\n          // 确保几何体居中\n          geometry.computeBoundingBox()\n\n          // 为导弹壳体模型设置特殊材质\n          let material\n          if (stlName === 'daodan.STL') {\n            material = new THREE.MeshPhongMaterial({\n              color: 0xffffff, // 白色\n              transparent: true,\n              opacity: 0.10, // 更高的透明度\n              side: THREE.DoubleSide, // 双面渲染\n              depthWrite: false, // 禁用深度写入以避免渲染问题\n              depthTest: false, // 禁用深度测试，使射线能够穿透\n              shininess: 150 // 增加光泽度\n            })\n          } else {\n            // 检查是否为需要设置为绿色的部件\n            const partName = stlName.split('.')[0] // 去掉.STL后缀\n            let color = DEFAULT_COLOR // 默认蓝色\n\n            // 检查是否为需要设置为绿色的部件\n            for (const greenPart of GREEN_PARTS) {\n              if (partName.includes(greenPart)) {\n                color = PART_COLOR // 设置为绿色\n                break\n              }\n            }\n\n            material = new THREE.MeshStandardMaterial({\n              color: color, // 使用确定的颜色\n              transparent: true,\n              opacity: 0.7\n            })\n          }\n\n          this.module = new THREE.Mesh(geometry, material) // 创建网格对象\n          this.module.name = stlName.split('.')[0] // 去掉.STL后缀\n          console.log('创建模型部件:', this.module.name)\n          this.info.name = '模型正在加载...'\n\n          // 旋转模型使底座底面与网格平行\n          this.module.rotateX(-Math.PI / 2) // 绕X轴旋转，使模型水平放置\n          this.module.rotateX(Math.PI / 2) // 绕X轴顺时针旋转90度\n\n          // 将所有模型添加到模型组\n          this.moduleAll.add(this.module)\n\n          // 如果是导弹模型，需要特殊处理\n          if (stlName === 'daodan.STL') {\n            console.log('处理导弹壳体模型位置')\n\n            // 设置导弹壳体的特殊属性，使其不阻挡交互\n            this.module.renderOrder = -1 // 负数renderOrder确保它在其他对象之前渲染\n            this.module.userData.isBackground = true // 标记为背景对象\n\n            // 计算所有模型的包围盒\n            const box = new THREE.Box3().setFromObject(this.moduleAll)\n            const center = box.getCenter(new THREE.Vector3())\n            const size = box.getSize(new THREE.Vector3())\n\n            // 重置导弹模型的位置和旋转\n            this.module.position.set(0, 0, 0)\n            this.module.rotation.set(0, 0, 0)\n\n            // 首先水平放置导弹模型\n            this.module.rotateX(-Math.PI / 2)\n\n            // 然后逆时针旋转90度，使导弹壳体与其他零件平行\n            this.module.rotateZ(Math.PI / 2)\n\n            // 计算导弹模型的包围盒\n            const daodanBox = new THREE.Box3().setFromObject(this.module)\n            const daodanSize = daodanBox.getSize(new THREE.Vector3())\n\n            // 调整导弹模型的缩放，确保能包含所有模型\n            // 增加缩放系数，使导弹壳体更大，伺服系统可以完全放在内部\n            const scaleFactor = Math.max(\n              size.x / daodanSize.x,\n              size.y / daodanSize.y,\n              size.z / daodanSize.z\n            ) * 2.5 // 放大3.5倍，确保伺服系统完全位于内部\n\n            this.module.scale.set(scaleFactor, scaleFactor, scaleFactor)\n\n            // 将导弹模型放置在所有模型的中心\n            this.module.position.copy(center)\n\n            // 计算导弹壳体的尺寸\n            const scaledDaodanLength = daodanSize.z * scaleFactor\n            const scaledDaodanWidth = daodanSize.x * scaleFactor\n            const scaledDaodanHeight = daodanSize.y * scaleFactor\n\n            // 根据图片反馈调整位置\n            // 向下移动导弹壳体（Y轴负方向）\n            this.module.position.y -= size.y * 1.3 // 向下移动\n\n            // 向右移动导弹壳体（Z轴正方向，由于旋转了90度）\n            this.module.position.z += size.z * 3.7 // 向右移动\n\n            // 向后移动导弹壳体（X轴正方向，由于旋转了90度）\n            this.module.position.x += scaledDaodanLength * 0.6// 向后移动\n\n            console.log('导弹壳体模型已调整位置和大小', {\n              center,\n              size,\n              scaleFactor,\n              daodanSize,\n              scaledDaodanLength,\n              scaledDaodanWidth,\n              scaledDaodanHeight,\n              adjustedPosition: this.module.position\n            })\n\n            // 延迟初始化火箭尾焰特效，确保导弹模型完全加载和定位\n            setTimeout(() => {\n              this.initRocketFlameEffect()\n            }, 800)\n          }\n\n          // 如果加载完成底座，说明主要模型都已加载完成\n          if (stlName === '底座.STL') {\n            // 调整整个模型组的位置，使其居中\n            this.moduleAll.position.set(0, 0, 0)\n\n            // 计算整个模型组的包围盒，用于居中\n            const box = new THREE.Box3().setFromObject(this.moduleAll)\n            const center = box.getCenter(new THREE.Vector3())\n\n            // 将模型移动到网格中央\n            this.moduleAll.position.x = -center.x\n            this.moduleAll.position.z = -center.z\n            // 保持y轴位置，使底座底面与网格平行\n            this.moduleAll.position.y = -box.min.y + 10 // 稍微抬高一点，避免穿过网格\n\n            this.m.close()\n            this.$notify.success({\n              title: '提示',\n              message: '模型加载完毕'\n            })\n            this.info.name = '待单击模型...'\n            console.log('模型已加载并居中放置')\n\n            // 打印所有模型部件名称，用于调试\n            console.log('所有模型部件:')\n            this.scene.traverse((object) => {\n              if (object.isMesh) {\n                console.log(' - ' + object.name)\n              }\n            })\n\n            // 确保所有部件都应用了正确的颜色\n            if (this.modelInteraction) {\n              console.log('应用颜色设置...')\n              this.modelInteraction.resetAllModelsColor()\n            }\n          }\n        }\n      )\n    },\n\n    // 改变显示组件重新绘制\n    loadPre() { // 加载哪个零件\n      // 先加载除导弹外的所有主要部件\n      let daodanModel = null\n      const otherModels = []\n\n      // 遍历所有模型文件\n      for (var i = 0; i < this.files.length; i++) {\n        var stlName = this.files[i].split('/')[1]\n        // 分开处理导弹模型和其他模型\n        if (stlName === 'daodan.STL') {\n          // 先记录导弹模型，稍后加载\n          daodanModel = stlName\n        } else if (!stlName.includes('GB╱T') && !stlName.includes('螺钉')) {\n          // 只加载主要零件，减少加载时间\n          this.loadSTL(stlName)\n          otherModels.push(stlName)\n        }\n      }\n\n      // 在所有其他模型加载完成后，最后加载导弹壳体模型\n      if (daodanModel) {\n        setTimeout(() => {\n          console.log('加载导弹壳体模型，包含所有其他模型')\n          this.loadSTL(daodanModel)\n        }, 1000) // 延迟加载，确保其他模型已经加载完成\n      }\n    },\n\n    // 调整导弹模型位置\n    adjustDaodanPosition() {\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (!daodanModel) return\n\n      // 计算所有其他模型的包围盒\n      const otherModelsBox = new THREE.Box3()\n      this.scene.traverse((object) => {\n        if (object.isMesh && object.name !== 'daodan') {\n          otherModelsBox.expandByObject(object)\n        }\n      })\n\n      // 计算导弹模型的包围盒\n      const daodanBox = new THREE.Box3().setFromObject(daodanModel)\n\n      // 调整导弹模型的位置，使其包含所有其他模型\n      // 并将其他模型放在导弹模型的尾部\n      const otherModelsCenter = otherModelsBox.getCenter(new THREE.Vector3())\n      const daodanCenter = daodanBox.getCenter(new THREE.Vector3())\n\n      // 计算需要移动的距离\n      const offsetX = otherModelsCenter.x - daodanCenter.x\n      const offsetY = otherModelsCenter.y - daodanCenter.y\n      const offsetZ = otherModelsCenter.z - daodanCenter.z\n\n      // 移动导弹模型\n      daodanModel.position.set(\n        offsetX,\n        offsetY,\n        offsetZ\n      )\n\n      console.log('调整了导弹模型位置')\n    },\n\n    // 数据面板\n    dataPanel(cw, ch, r, k, x, y, z) {\n      // 用canvas生成图片\n      var color = ['#008000', '#FF0000']\n      var canvas = document.createElement('canvas')\n      const ctx = canvas.getContext('2d')\n      canvas.width = cw\n      canvas.height = ch\n      ctx.lineWidth = 10\n      ctx.fillStyle = 'rgba(255,255,255,1)'\n      this.roundRect(ctx, 0, 0, cw, ch, r)\n      var gradient = ctx.createLinearGradient(0, 0, canvas.width, 0)\n      gradient.addColorStop('0', 'blue')\n      gradient.addColorStop('1.0', 'red')\n      ctx.font = 'normal 80pt \"楷体\"'\n      ctx.fillStyle = color[this.objectStatus.status]\n      ctx.fillText(this.statusName, 700, 150)\n      ctx.fillStyle = color[0]\n      // ctx.fillText('0.83', 700, 300)\n      ctx.fillText(this.dataShow.healthStatus, 700, 300)\n      ctx.fillStyle = gradient\n      ctx.fillText('当前状态：', 60, 150)\n      ctx.fillText('健康值：', 60, 300)\n      ctx.fillText('位移指令：', 60, 450)\n      ctx.fillText('实际位移：', 60, 600)\n      ctx.fillText('负载力：', 60, 750)\n      ctx.fillText(this.dataShow.Xgive, 700, 450)\n      ctx.fillText(this.dataShow.Xget, 700, 600)\n      ctx.fillText(this.dataShow.Fget, 700, 750)\n      // canvas.height = 500\n      const url = canvas.toDataURL('./img/png')\n      var geometry = new THREE.PlaneGeometry(cw / k, ch / k)\n      var texture = new THREE.TextureLoader().load(url)\n      // 将图像加载为纹理，然后将纹理赋给材质的map属性\n      var material = new THREE.MeshBasicMaterial({\n        map: texture,\n        side: THREE.DoubleSide,\n        opacity: 1,\n        transparent: true\n      })\n      const rect = new THREE.Mesh(geometry, material)\n      rect.position.set(x, z, y)\n      this.scene.add(rect)\n    },\n\n    // 画圆角矩形\n    roundRect(ctx, x, y, w, h, r) {\n      ctx.beginPath()\n      ctx.moveTo(x + r, y)\n      ctx.arcTo(x + w, y, x + w, y + h, r)\n      ctx.arcTo(x + w, y + h, x, y + h, r)\n      ctx.arcTo(x, y + h, x, y, r)\n      ctx.arcTo(x, y, x + w, y, r)\n      ctx.fill()\n      ctx.closePath()\n    },\n\n    // 监听函数部分 - 使用新的模型交互工具\n    onMouseClick(event) {\n      // 加载字典数据\n      this.$axios.get('./errorDict.json').then(res => {\n        this.p2hDict = res.data.pinyin2hanzi\n        const obj = this.p2hDict\n        this.hanziList = Object.keys(obj)\n      })\n\n      // 确保导弹壳体始终保持透明\n      const daodanModel = this.scene.getObjectByName('daodan')\n      if (daodanModel && daodanModel.material) {\n        this.modelInteraction.updateDaodanMaterial(daodanModel)\n      }\n\n      const element = document.getElementById('model-container')\n      var raycaster = new THREE.Raycaster()\n      var mouse = new THREE.Vector2()\n\n      // 将鼠标点击位置的屏幕坐标转成threejs中的标准坐标\n      mouse.x = (event.offsetX / element.clientWidth) * 2 - 1\n      mouse.y = -(event.offsetY / element.clientHeight) * 2 + 1\n\n      // 通过摄像机和鼠标位置更新射线\n      raycaster.setFromCamera(mouse, this.camera)\n\n      // 计算物体和射线的焦点\n      var intersects = raycaster.intersectObjects(this.scene.children, true)\n        .filter(intersect => {\n          // 过滤掉导弹壳体，使其不响应点击\n          return intersect.object.name !== 'daodan' &&\n                 !intersect.object.userData.isBackground\n        })\n\n      if (intersects.length > 0) {\n        // 处理点击到的对象\n        this.modelInteraction.handleModelClick(intersects[0].object)\n      } else {\n        // 点击到空白区域\n        this.modelInteraction.handleBackgroundClick()\n      }\n    },\n\n    // 检测区域大小变化\n    onWindowResize() {\n      const element = document.getElementById('model-container')\n      this.camera.aspect = element.clientWidth / element.clientHeight\n      this.camera.updateProjectionMatrix()\n      this.render()\n      // 设置渲染区域尺寸\n      this.renderer.setSize(element.clientWidth, element.clientHeight)\n      console.log('3d area changes')\n\n      // 恢复到初始正常状态\n      this.resetAllPartsStatus()\n\n      // 清除Vuex中的诊断结果\n      this.$store.dispatch('diagnosis/clearDiagnosisResult')\n\n      // 重置信息显示\n      this.info.name = '待单击模型...'\n\n      console.log('已恢复到初始正常状态')\n    },\n\n    // 处理view-details事件\n    handleViewDetails() {\n      console.log('查看详情:', this.currentFaultType, this.currentFaultPart)\n\n      // 如果当前有故障或退化状态\n      if (this.currentFaultType && this.currentFaultPart) {\n        // 显示详细信息对话框\n        this.$alert(`\n          <div class=\"fault-details\">\n            <h3>${this.faultNotificationStatusName}</h3>\n            <p><strong>部件名称:</strong> ${this.faultNotificationPartName}</p>\n            <p><strong>故障类型:</strong> ${this.currentFaultType}</p>\n            <p><strong>诊断时间:</strong> ${this.faultNotificationTime}</p>\n            <p><strong>可能原因:</strong> ${this.getPossibleCauses(this.currentFaultType)}</p>\n            <p><strong>建议解决方案:</strong> ${this.getSuggestedSolutions(this.currentFaultType)}</p>\n          </div>\n        `, '故障详情', {\n          dangerouslyUseHTMLString: true,\n          confirmButtonText: '确定',\n          callback: action => {\n            console.log(action)\n          }\n        })\n      }\n    },\n\n    // 获取可能原因\n    getPossibleCauses(faultType) {\n      const causes = {\n        '1_degradation_magnet': '长时间使用导致永磁体性能下降；环境温度过高；机械冲击或振动。',\n        '2_degradation_brush_wear': '正常磨损；过载运行；环境中存在过多粉尘。',\n        '3_degradation_commutator_oxidation': '环境湿度过高；长期不使用；电刷与换向器接触不良。',\n        '4_fault_stator_short': '绝缘材料老化；过载运行；绕组温度过高；制造缺陷。',\n        '5_fault_rotor_open': '机械损伤；过载运行；焊接点断裂；制造缺陷。',\n        '6_degradation_bearing_wear': '正常磨损；润滑不足；轴承负载过大；轴承安装不当。',\n        '7_fault_bearing_stuck': '润滑失效；异物进入；轴承严重磨损；轴承锈蚀。',\n        '8_degradation_gear_wear': '正常磨损；润滑不足；齿轮负载过大；齿轮材料缺陷。',\n        '9_degradation_sensor_drift': '长期使用导致性能下降；环境温度变化；电源电压波动。',\n        '10_fault_sensor_loss': '传感器连接松动；传感器损坏；信号线断路；电源故障。',\n        '11_fault_mosfet_breakdown': '过电压；过电流；温度过高；静电放电损伤。',\n        '12_degradation_drive_distortion': '电路元件老化；电源电压不稳；信号干扰；温度变化。',\n        '13_fault_mcu_crash': '软件错误；电源问题；硬件故障；外部干扰。'\n      }\n      return causes[faultType] || '未知原因'\n    },\n\n    // 获取建议解决方案\n    getSuggestedSolutions(faultType) {\n      const solutions = {\n        '1_degradation_magnet': '更换永磁体；降低工作环境温度；减少机械冲击。',\n        '2_degradation_brush_wear': '更换电刷；检查负载是否过大；清洁工作环境。',\n        '3_degradation_commutator_oxidation': '清洁换向器表面；降低环境湿度；定期维护。',\n        '4_fault_stator_short': '更换定子绕组；检查负载情况；改善散热条件。',\n        '5_fault_rotor_open': '更换转子绕组；检查焊接点；降低负载。',\n        '6_degradation_bearing_wear': '更换轴承；增加润滑；检查轴承安装情况。',\n        '7_fault_bearing_stuck': '更换轴承；清洁轴承；检查润滑情况。',\n        '8_degradation_gear_wear': '更换齿轮；增加润滑；检查负载情况。',\n        '9_degradation_sensor_drift': '校准传感器；更换传感器；稳定工作环境。',\n        '10_fault_sensor_loss': '检查连接；更换传感器；检查信号线路。',\n        '11_fault_mosfet_breakdown': '更换MOSFET；检查电路保护措施；改善散热条件。',\n        '12_degradation_drive_distortion': '更换老化元件；稳定电源电压；增加信号滤波。',\n        '13_fault_mcu_crash': '更新软件；检查电源；更换MCU；增加抗干扰措施。'\n      }\n      return solutions[faultType] || '请联系专业维修人员'\n    }\n  }\n}\n</script>\n<style>\n#model-container {\n  border:solid 5px red;\n  position: absolute;\n  width: 100%;\n  height: 100%;\n}\n.ctl {\n  position: absolute;\n  left:39%;\n  top:0\n}\n.label-col {\n  padding: 8px 5px;\n}\n#gui_container{\n  position: absolute;\n  top: 84%;\n  left: 81%;\n}\n#gui{\n/* 表示gui.domElement没有height属性 */\n  transform:translate(-50%, -75px);\n}\n#infoBox {\n  position: absolute;\n  padding: 5px;\n  background: #7373741a;\n  border: 3px double whitesmoke;\n  border-radius: 12px;\n  color: whitesmoke;\n  font-size:17px;\n  min-width: 160px;\n  height: 90px;\n  width: 380px\n}\n\n/* 伺服系统信息铭牌样式 */\n#systemInfoBox {\n  position: absolute;\n  right: 20px;\n  top: 20px;\n  padding: 10px;\n  background: #7373741a;\n  border: 3px double whitesmoke;\n  border-radius: 12px;\n  color: whitesmoke;\n  font-size: 14px;\n  min-width: 200px;\n  width: 280px;\n}\n\n#systemInfoBox h4 {\n  text-align: center;\n  margin-top: 0;\n  margin-bottom: 10px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #e6e6e6;\n}\n\n#systemInfoBox ul {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n#systemInfoBox li {\n  margin: 5px 0;\n  line-height: 1.5;\n}\n\n#systemInfoBox strong {\n  display: inline-block;\n  width: 85px;\n}\n\n\n\n/* 故障详情对话框样式 */\n.fault-details {\n  padding: 10px;\n}\n\n.fault-details h3 {\n  color: #F56C6C;\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 18px;\n  text-align: center;\n  border-bottom: 1px solid #EBEEF5;\n  padding-bottom: 10px;\n}\n\n.fault-details p {\n  margin: 10px 0;\n  line-height: 1.6;\n}\n\n.fault-details strong {\n  color: #303133;\n  display: inline-block;\n  width: 100px;\n  vertical-align: top;\n}\n\n/* 自定义El-Alert样式 */\n.el-message-box {\n  width: 500px !important;\n  max-width: 90%;\n}\n\n.el-message-box__content {\n  max-height: 60vh;\n  overflow-y: auto;\n}\n</style>\n\n"]}]}