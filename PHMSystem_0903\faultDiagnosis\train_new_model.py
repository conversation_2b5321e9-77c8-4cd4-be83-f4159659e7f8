import os
import numpy as np
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import GRU, Dense, Dropout
from tensorflow.keras.utils import to_categorical
from sklearn.model_selection import train_test_split
import datetime
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau
from sklearn.metrics import classification_report
from tensorflow.keras.optimizers import Adam

# --- 配置参数 ---
# 数据源目录 (相对于此脚本的位置)
DATA_DIR = os.path.join(os.path.dirname(__file__), '..', 'static', 'simulated_data_npz')
# 模型保存目录
MODEL_SAVE_DIR = os.path.join(os.path.dirname(__file__), 'model')

# 模型和训练参数
SEGMENT_LENGTH = 256  # 每个训练样本的时间序列长度
NUM_CLASSES = 14      # 类别总数 (0-13)
TEST_SIZE = 0.2       # 测试集所占比例
EPOCHS = 50           # 训练轮数
BATCH_SIZE = 64       # 批处理大小

def load_and_prepare_data(data_dir, segment_length, num_classes):
    """
    加载所有 .npz 数据，切片并准备为训练格式。
    """
    all_segments = []
    all_labels = []

    if not os.path.exists(data_dir):
        raise FileNotFoundError(f"数据目录未找到: {data_dir}")

    file_list = [f for f in os.listdir(data_dir) if f.endswith('.npz')]
    print(f"找到 {len(file_list)} 个数据文件，开始处理...")

    for filename in file_list:
        try:
            # 从文件名提取标签 (例如 '1_degradation_magnet.npz' -> 1)
            label = int(filename.split('_')[0])
            
            filepath = os.path.join(data_dir, filename)
            data = np.load(filepath)

            # 将所需通道堆叠成一个多维数组 (channels, timesteps)
            # 我们使用 'position', 'current', 'setpoint' 作为3个通道
            channels = [data['position'], data['current'], data['setpoint']]
            stacked_data = np.stack(channels, axis=1) # -> (timesteps, channels)

            # 标准化数据 (可选，但推荐)
            mean = np.mean(stacked_data, axis=0)
            std = np.std(stacked_data, axis=0)
            # 避免除以零
            std[std == 0] = 1
            standardized_data = (stacked_data - mean) / std
            
            # 将连续数据切分成固定长度的片段
            num_segments = len(standardized_data) // segment_length
            for i in range(num_segments):
                start = i * segment_length
                end = start + segment_length
                segment = standardized_data[start:end]
                all_segments.append(segment)
                all_labels.append(label)

        except Exception as e:
            print(f"处理文件 {filename} 时出错: {e}")
            continue

    if not all_segments:
        raise ValueError("未能从数据目录加载任何有效数据。")

    X = np.array(all_segments)
    y = np.array(all_labels)

    # 将标签转换为 one-hot 编码
    y_one_hot = to_categorical(y, num_classes=num_classes)

    print("数据加载和预处理完成。")
    print(f"总样本数: {X.shape[0]}, 样本形状: {X.shape[1:]}, 标签形状: {y_one_hot.shape}")
    
    return X, y_one_hot

def build_model(input_shape, num_classes):
    """
    构建 GRU 模型.
    """
    model = Sequential([
        # 输入层，形状为 (时间步长, 特征数)
        GRU(128, return_sequences=True, input_shape=input_shape),
        Dropout(0.3),
        # 第二个 GRU 层
        GRU(64, return_sequences=False),
        Dropout(0.3),
        # 全连接层
        Dense(64, activation='relu'),
        # 输出层，有 num_classes 个神经元，使用 softmax 进行多分类
        Dense(num_classes, activation='softmax')
    ])

    model.compile(optimizer='adam',
                  loss='categorical_crossentropy',
                  metrics=['accuracy'])
    
    model.summary()
    return model

def load_and_preprocess_data(data_path, sequence_length=100, step=1):
    """
    加载所有 .npz 数据，切片并准备为训练格式。
    """
    all_segments = []
    all_labels = []

    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据目录未找到: {data_path}")

    file_list = [f for f in os.listdir(data_path) if f.endswith('.npz')]
    print(f"找到 {len(file_list)} 个数据文件，开始处理...")

    for filename in file_list:
        try:
            # 从文件名中解析出标签
            # 文件名格式： "0_normal_sample_0.npz", "1_degradation_magnet_sample_15.npz"
            try:
                label_str = os.path.basename(filename).split('_')[0]
                label = int(label_str)
            except (ValueError, IndexError):
                print(f"警告: 无法从文件名 {filename} 中解析标签，已跳过。")
                continue
            
            filepath = os.path.join(data_path, filename)
            data = np.load(filepath)

            # 将所需通道堆叠成一个多维数组 (channels, timesteps)
            # 我们使用 'position', 'current', 'setpoint' 作为3个通道
            channels = [data['position'], data['current'], data['setpoint']]
            stacked_data = np.stack(channels, axis=1) # -> (timesteps, channels)

            # 标准化数据 (可选，但推荐)
            mean = np.mean(stacked_data, axis=0)
            std = np.std(stacked_data, axis=0)
            # 避免除以零
            std[std == 0] = 1
            standardized_data = (stacked_data - mean) / std
            
            # 将连续数据切分成固定长度的片段
            num_segments = len(standardized_data) // sequence_length
            for i in range(num_segments):
                start = i * sequence_length
                end = start + sequence_length
                segment = standardized_data[start:end]
                all_segments.append(segment)
                all_labels.append(label)

        except Exception as e:
            print(f"处理文件 {filename} 时出错: {e}")
            continue

    if not all_segments:
        raise ValueError("未能从数据目录加载任何有效数据。")

    X = np.array(all_segments)
    y = np.array(all_labels)

    # 将标签转换为 one-hot 编码
    y_one_hot = to_categorical(y, num_classes=NUM_CLASSES)

    print("数据加载和预处理完成。")
    print(f"总样本数: {X.shape[0]}, 样本形状: {X.shape[1:]}, 标签形状: {y_one_hot.shape}")
    
    return X, y_one_hot

def main():
    # 确保路径是相对于项目根目录的
    base_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..')) 
    
    # *** 使用新生成的数据集 ***
    data_dir = os.path.join(base_dir, 'static', 'simulated_data_npz_augmented')
    model_output_dir = os.path.join(base_dir, 'faultDiagnosis', 'models_28sy_v2')
    
    # 确保输出目录存在
    if not os.path.exists(model_output_dir):
        os.makedirs(model_output_dir)

    # 1. 加载和准备数据
    X_data, y_data = load_and_prepare_data(data_dir, SEGMENT_LENGTH, NUM_CLASSES)

    # 2. 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X_data, y_data, test_size=TEST_SIZE, random_state=42, stratify=np.argmax(y_data, axis=1)
    )
    print(f"训练集大小: {len(X_train)}, 测试集大小: {len(X_test)}")

    # 3. 构建模型
    input_shape = (X_train.shape[1], X_train.shape[2])
    model = build_model(input_shape, NUM_CLASSES)

    # 4. 设置高级回调函数
    # ModelCheckpoint: 在每个epoch后保存最优模型
    # EarlyStopping: 如果验证集损失在patience个epoch内没有改善，则提前停止训练
    # ReduceLROnPlateau: 当指标停止改善时，降低学习率
    callbacks = [
        ModelCheckpoint(filepath=os.path.join(model_output_dir, 'model_28sy_augmented.h5'), 
                        monitor='val_acc',
                        save_best_only=True,
                        verbose=1),
        EarlyStopping(monitor='val_loss', 
                      patience=10,
                      verbose=1),
        ReduceLROnPlateau(monitor='val_loss', 
                          factor=0.2, 
                          patience=5,
                          min_lr=0.00001,
                          verbose=1)
    ]

    # 5. 训练模型
    print("\n开始训练模型...")
    history = model.fit(
        X_train, y_train,
        epochs=100,  # 增加训练轮次
        batch_size=64, # 增大数据批次
        validation_data=(X_test, y_test),
        callbacks=callbacks,
        verbose=1
    )

    # 6. 评估模型
    loss, accuracy = model.evaluate(X_test, y_test, verbose=0)
    print("\n模型训练完成。")
    print(f"测试集损失: {loss:.4f}")
    print(f"测试集准确率: {accuracy:.4f}")

    # 7. 打印分类报告
    print("\n分类报告:")
    y_pred_classes = np.argmax(model.predict(X_test), axis=1)
    y_test_classes = np.argmax(y_test, axis=1)

    # 定义所有可能的标签名称
    target_names = [
        '0_normal', '1_degradation_magnet', '2_degradation_brush_wear', 
        '3_degradation_commutator_oxidation', '4_fault_stator_short', 
        '5_fault_rotor_open', '6_degradation_bearing_wear', '7_fault_bearing_stuck',
        '8_degradation_gear_wear', '9_degradation_sensor_drift', '10_fault_sensor_loss',
        '11_fault_mosfet_breakdown', '12_degradation_drive_distortion', '13_fault_mcu_crash'
    ]
    
    print(classification_report(y_test_classes, y_pred_classes, target_names=target_names))

    print(f"\n模型训练完毕。最优模型已保存至 {model_output_dir}/model_28sy_augmented.h5")

if __name__ == '__main__':
    main() 