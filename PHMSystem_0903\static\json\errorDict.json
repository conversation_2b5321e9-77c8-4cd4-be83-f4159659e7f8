{"errorDict": {"svpwmControl_normal": 0, "EMAsvpwm2020a_unload_normal": 0, "svpwmControl_Akailu": 1, "svpwmControl_Bkailu": 2, "svpwmControl_Ckailu": 3, "svpwmControl_kailu_A_IGBTShang": 4, "svpwmControl_kailu_A_IGBTXia": 5, "svpwmControl_kailu_B_IGBTShang": 6, "svpwmControl_kailu_B_IGBTXia": 7, "svpwmControl_kailu_C_IGBTShang": 8, "svpwmControl_kailu_C_IGBTXia": 9, "EMAsvpwm2020_sensor_weiyi": 10, "EMAsvpwm2020_sensor_sudu": 11, "EMAsvpwm2020_shici": 12, "EMAsvpwm2020a_AB_xiangjianduanlu": 13, "EMAsvpwm2020a_BC_xiangjianduanlu": 14, "EMAsvpwm2020a_AC_xiangjianduanlu": 15, "EMAsvpwm2020_A_zajianduanlu": 16, "EMAsvpwm2020_B_zajianduanlu": 17, "EMAsvpwm2020_C_zajianduanlu": 18}, "errorNameDict": {"0": "正常", "1": "A相开路", "2": "B相开路", "3": "C相开路", "4": "A上桥臂IGBT开路", "5": "A下桥臂IGBT开路", "6": "B上桥臂IGBT开路", "7": "B下桥臂IGBT开路", "8": "C上桥臂IGBT开路", "9": "C下桥臂IGBT开路", "10": "位移传感器故障", "11": "速度传感器故障", "12": "永磁体失磁故障", "13": "AB相间短路", "14": "BC相间短路", "15": "AC相间短路", "16": "A相匝间短路故障", "17": "B匝间短路故障", "18": "C相匝间短路故障"}, "errorName2IndexDict": {"正常": "0", "A相开路": "1", "B相开路": "2", "C相开路": "3", "A上桥臂IGBT开路": "4", "A下桥臂IGBT开路": "5", "B上桥臂IGBT开路": "6", "B下桥臂IGBT开路": "7", "C上桥臂IGBT开路": "8", "C下桥臂IGBT开路": "9", "位移传感器故障": "10", "速度传感器故障": "11", "永磁体失磁故障": "12", "AB相间短路": "13", "BC相间短路": "14", "AC相间短路": "15", "A相匝间短路故障": "16", "B相匝间短路故障": "17", "C相匝间短路故障": "18"}, "errorIndex2ENameDict": {"0": "svpwmControl_normal", "1": "svpwmControl_Akailu", "2": "svpwmControl_Bkailu", "3": "svpwmControl_Ckailu", "4": "svpwmControl_kailu_A_IGBTShang", "5": "svpwmControl_kailu_A_IGBTXia", "6": "svpwmControl_kailu_B_IGBTShang", "7": "svpwmControl_kailu_B_IGBTXia", "8": "svpwmControl_kailu_C_IGBTShang", "9": "svpwmControl_kailu_C_IGBTXia", "10": "EMAsvpwm2020_sensor_weiyi", "11": "EMAsvpwm2020_sensor_sudu", "12": "EMAsvpwm2020_shici", "13": "EMAsvpwm2020a_AB_xiangjianduanlu", "14": "EMAsvpwm2020a_BC_xiangjianduanlu", "15": "EMAsvpwm2020a_AC_xiangjianduanlu", "16": "EMAsvpwm2020_A_zajianduanlu", "17": "EMAsvpwm2020_B_zajianduanlu", "18": "EMAsvpwm2020_C_zajianduanlu", "00": "EMAsvpwm2020a_unload_normal"}}